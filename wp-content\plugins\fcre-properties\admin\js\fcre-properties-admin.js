(function ($) {
    $(document).ready(function () {
        'use strict';

        // Update hidden input field with the selected post IDs
        function updateHiddenInput($wrapper) {
            var ids = [];
            $wrapper.find('.fcre-selected-list .fcre-item').each(function(){
                ids.push($(this).data('id'));
            });
            $wrapper.find('.fcre-hidden-input').val(ids.join(','));
        }

        // Main wrapper
        $('.fcre-relationship-wrapper').each(function(){
            var $wrapper = $(this);

            // Available Search Filter
            $wrapper.find('.fcre-search').on('input', function(){
                var searchTerm = $(this).val().toLowerCase();
                $wrapper.find('.fcre-available-list .fcre-item').each(function(){
                    var text = $(this).text().toLowerCase();
                    if (text.indexOf(searchTerm) !== -1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });

            // Move from available to selected
            $wrapper.on('click', '.fcre-available-list .fcre-item:not(.fcre-disabled)', function(){
                var $item = $(this).clone();
                $item.append('<span class="fcre-remove" title="Remove">×</span>');
                $wrapper.find('.fcre-selected-list').append($item);
                $(this).addClass('fcre-disabled').css('pointer-events', 'none').css('opacity', '0.5');
                updateHiddenInput($wrapper);
            });

            // Remove from selected
            $wrapper.on('click', '.fcre-selected-list .fcre-remove', function(e){
                e.stopPropagation();
                var $li = $(this).closest('.fcre-item');
                var id = $li.data('id');

                // Re-enable the item in the available list
                $wrapper.find('.fcre-available-list .fcre-item[data-id="' + id + '"]')
                        .removeClass('fcre-disabled')
                        .css('pointer-events', 'auto')
                        .css('opacity', '1');
                
                // Remove the item from selected list
                $li.remove();
                updateHiddenInput($wrapper);
            });

            // Make selected list sortable
            $wrapper.find('.fcre-selected-list').sortable({
                placeholder: 'fcre-placeholder',
                update: function() {
                    updateHiddenInput($wrapper);
                }
            }).disableSelection();
        });

    });


jQuery(document).ready(function ($) {
    function bindUploadButtons() {
        $('.fcre-upload-btn').off('click').on('click', function (e) {
            e.preventDefault();

            const button = $(this);
            const wrapper = button.closest('.fcre-file-upload-wrapper');
            const input = wrapper.find('input[type="hidden"]');
            const uploader = wrapper.find('.fcre-file-uploader');

            // Add loading state
            uploader.addClass('loading');

            const file_frame = wp.media({
                title: 'Select or Upload a File',
                button: {
                    text: 'Use this file'
                },
                multiple: false
            });

            file_frame.on('select', function () {
                const attachment = file_frame.state().get('selection').first().toJSON();

                // Remove loading state
                uploader.removeClass('loading error').addClass('success');

                // Update the hidden input with attachment ID
                input.val(attachment.id);

                // Get file extension
                const fileName = attachment.filename || attachment.title;
                const fileExt = fileName.split('.').pop().toLowerCase();
                const fileSize = attachment.filesizeHumanReadable || '';

                // Create the new file preview HTML
                const previewHTML = `
                    <div class="fcre-file-preview-container">
                        <div class="fcre-file-icon-wrapper">
                            <a href="${attachment.url}"
                               class="fcre-file-link ${fileExt}"
                               target="_blank"
                               title="${fileName}">
                                ${fileName}
                            </a>
                        </div>
                        <div class="fcre-file-details">
                            <div class="fcre-file-name">${fileName}</div>
                            <div class="fcre-file-size">${fileSize}</div>
                        </div>
                        <div class="fcre-file-actions">
                            <button type="button" class="button fcre-upload-btn fcre-change-file" title="Change File">
                                <span class="dashicons dashicons-edit"></span>
                            </button>
                            <button type="button" class="button fcre-remove-btn fcre-remove-file" title="Remove File">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </div>
                    </div>
                `;

                // Update the uploader container
                uploader.removeClass('no-file').addClass('has-file').html(previewHTML);

                // Re-bind events for the new buttons
                bindUploadButtons();
                bindDragAndDrop();

                // Remove success state after a delay
                setTimeout(() => {
                    uploader.removeClass('success');
                }, 2000);
            });

            file_frame.on('close', function () {
                uploader.removeClass('loading');
            });

            file_frame.open();
        });

        $('.fcre-remove-btn').off('click').on('click', function (e) {
            e.preventDefault();

            const button = $(this);
            const wrapper = button.closest('.fcre-file-upload-wrapper');
            const input = wrapper.find('input[type="hidden"]');
            const uploader = wrapper.find('.fcre-file-uploader');
            const label = wrapper.find('label').text();
            const placeholder = label ? label.replace('*', '').trim() : 'Upload File';

            // Clear the input value
            input.val('');

            // Reset to upload area
            const uploadHTML = `
                <div class="fcre-file-upload-area">
                    <div class="fcre-upload-placeholder">
                        <span class="dashicons dashicons-upload"></span>
                        <p>${placeholder}</p>
                        <button type="button" class="button button-primary fcre-upload-btn">Select File</button>
                    </div>
                </div>
            `;

            uploader.removeClass('has-file success error').addClass('no-file').html(uploadHTML);

            // Re-bind events for the new button
            bindUploadButtons();
            bindDragAndDrop();
        });
    }

    function bindDragAndDrop() {
        $('.fcre-file-upload-area').off('dragover dragenter dragleave drop').on({
            'dragover dragenter': function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            },
            'dragleave': function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
            },
            'drop': function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    // For now, just open the media uploader
                    // In the future, you could implement direct file upload here
                    $(this).find('.fcre-upload-btn').trigger('click');
                }
            }
        });
    }

    bindUploadButtons();
    bindDragAndDrop();
});


})(jQuery);
