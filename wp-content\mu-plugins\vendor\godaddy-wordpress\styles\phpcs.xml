<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="GoDaddy_Styles" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/squizlabs/PHP_CodeSniffer/master/phpcs.xsd">
	<description>Rules for GoDaddy_Styles</description>

	<arg name="extensions" value="php" />
	<arg name="basepath" value="./" />
	<file>./</file>

	<exclude-pattern>/build/*</exclude-pattern>
	<exclude-pattern>/vendor/*</exclude-pattern>
	<exclude-pattern>/node_modules/*</exclude-pattern>

	<arg name="colors"/>
	<arg value="sp"/>

	<!-- Include the WordPress-Extra standard. -->
	<rule ref="WordPress-Extra">
		<!--
		We may want a middle ground though. The best way to do this is add the
		entire ruleset, then rule by rule, remove ones that don't suit a project.
		We can do this by running `phpcs` with the '-s' flag, which allows us to
		see the names of the sniffs reporting errors.
		Once we know the sniff names, we can opt to exclude sniffs which don't
		suit our project like so.
		-->
		<exclude name="WordPress.Files.FileName"/>
		<!-- <exclude name="WordPress.NamingConventions.ValidFunctionName.MethodNameInvalid"/> -->
		<!-- <exclude name="WordPress.Files.FileName.InvalidClassFileName"/> -->
		<!-- <exclude name="WordPress.Files.FileName.NotHyphenatedLowercase"/> -->
	</rule>

	<!-- Let's also check that everything is properly documented. -->
	<rule ref="WordPress-Docs"/>

	<!-- Add in some extra rules from other standards. -->
	<rule ref="Generic.CodeAnalysis.UnusedFunctionParameter"/>
	<rule ref="Generic.Commenting.Todo"/>

	<!-- Check for PHP cross-version compatibility. -->
	<config name="testVersion" value="7.4-"/>
	<rule ref="PHPCompatibilityWP"/>

	<!--
	To get the optimal benefits of using WPCS, we should add a couple of
	custom properties.
	Adjust the values of these properties to fit our needs.

	For information on additional custom properties available, check out
	the wiki:
	https://github.com/WordPress-Coding-Standards/WordPress-Coding-Standards/wiki/Customizable-sniff-properties
	-->
	<config name="minimum_supported_wp_version" value="5.9"/>

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="godaddy-styles"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array">
				<element value="gds"/>
				<element value="GoDaddy\Styles"/>
			</property>
		</properties>
	</rule>

</ruleset>
