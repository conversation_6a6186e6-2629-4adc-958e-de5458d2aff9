<?php
$FCRE = Fcre_Global::getInstance();

// Extract variables from $args if they exist (passed from shortcode)
if (isset($args) && is_array($args)) {
    extract($args);
}
// Parse shortcode parameters if they exist
$property_types_selected = [];
$transaction_types_selected = [];
$property_status_selected = [];
$address_value = '';
$min_sf_value = '';
$max_sf_value = '';
$min_year_value = '';
$max_year_value = '';

// Handle property_types parameter
if (!empty($property_types)) {
    $property_types_selected = array_map('trim', explode(',', $property_types));
}

// Handle transaction_types parameter
if (!empty($transaction_types)) {
    $transaction_types_selected = array_map('trim', explode(',', $transaction_types));
}

// Handle property_status parameter
if (!empty($property_status)) {
    $property_status_selected = array_map('trim', explode(',', $property_status));
}

// Handle other parameters
if (!empty($address)) {
    $address_value = $address;
}
if (!empty($min_sf)) {
    $min_sf_value = $min_sf;
}
if (!empty($max_sf)) {
    $max_sf_value = $max_sf;
}
if (!empty($min_year)) {
    $min_year_value = $min_year;
}
if (!empty($max_year)) {
    $max_year_value = $max_year;
}
?>
    <div class="fcre-container-fluid fcre-filter-wrapper">
        <div class="fcre-row fcre-py-3">
            <div class="fcre-col-xl-2 fcre-col-lg-4 fcre-col-md-6 fcre-col-sm-12">
                <div class="fcre-form-group">
                    <label>Search</label>
                    <input id="address" name="address" type="text" class="fcre-form-control" placeholder="Search by address, city, state, or zip" value="<?php echo esc_attr($address_value); ?>">
                </div>
            </div>
            <div class="fcre-col-xl-2 fcre-col-lg-4 fcre-col-md-6 fcre-col-sm-12">
                <?php
                fcre_frontend_multiselect([
                    'option_key' => $FCRE->plugin_name . '-property-types',
                    'field_name' => 'property_types',
                    'label' => 'Property Types',
                    'placeholder' => 'Select Property Types',
                    'selected_values' => $property_types_selected,
                ]);
                ?>
            </div>
            <div class="fcre-col-xl-2 fcre-col-lg-4 fcre-col-md-6 fcre-col-sm-12">
                <?php
                fcre_frontend_multiselect([
                    'option_key' =>  $FCRE->plugin_name . '-transaction-types',
                    'field_name' => 'transaction_types',
                    'label' => 'Transaction Types',
                    'placeholder' => 'Select Transaction Types',
                    'selected_values' => $transaction_types_selected,
                ]);
                ?>
            </div>
            <div class="fcre-col-xl-2 fcre-col-lg-4 fcre-col-md-6 fcre-col-sm-12">
                <?php
                fcre_frontend_multiselect([
                    'option_key' =>  $FCRE->plugin_name . '-property-status',
                    'field_name' => 'property_status',
                    'label' => 'Property Status',
                    'placeholder' => 'Select Property Status',
                    'selected_values' => $property_status_selected,
                ]);
                ?>
            </div>
            <div class="fcre-col-xl-2 fcre-col-lg-4 fcre-col-md-6 fcre-col-sm-12">
                <div class="fcre-form-group">
                    <label>Building Size</label>
                    <div class="fcre-range-input">
                    <input id="min-sf" name="min-sf" type="number" class="fcre-form-control" placeholder="Min SF" value="<?php echo esc_attr($min_sf_value); ?>">
                    <span>to</span>
                    <input id="max-sf" name="max-sf" type="number" class="fcre-form-control" placeholder="Max SF" value="<?php echo esc_attr($max_sf_value); ?>">
                    </div>
                </div>
            </div>
            <div class="fcre-col-xl-2 fcre-col-lg-4 fcre-col-md-6 fcre-col-sm-12">
                <div class="fcre-form-group">
                    <label>Year Built</label>
                    <div class="fcre-range-input">
                    <input id="min-year" name="min-year" type="number" class="fcre-form-control" placeholder="Min Year" value="<?php echo esc_attr($min_year_value); ?>">
                    <span>to</span>
                    <input id="max-year" name="max-year" type="number" class="fcre-form-control" placeholder="Max Year" value="<?php echo esc_attr($max_year_value); ?>">
                    </div>
                </div>
            </div>
        </div>
    </div>
