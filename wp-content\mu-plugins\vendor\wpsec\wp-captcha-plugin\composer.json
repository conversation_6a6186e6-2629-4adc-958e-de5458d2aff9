{"description": "WPSEC - WordPress Captcha Plugin (Bruteforce plugin that secure login with image captcha and IP check)", "name": "wpsec/wp-captcha-plugin", "type": "wordpress-plugin", "repositories": [{"type": "vcs", "url": "**************:gdcorp-wordpress/wpsec-wp-plugin-tests.git"}], "require": {"ext-gd": "*"}, "require-dev": {"wpsec/wp-mock-test-abstraction": "dev-main", "phpunit/phpunit": "^9.5", "wp-coding-standards/wpcs": "^2.3.0", "squizlabs/php_codesniffer": "3.*", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.1", "10up/wp_mock": "0.4.2"}, "autoload": {"psr-4": {"Wpsec\\captcha\\": "src/"}}, "autoload-dev": {"psr-4": {"Wpsec\\captcha\\Tests\\": "tests/"}}, "scripts": {"phpunit": "vendor/bin/phpunit --configuration tests/phpunit/phpunit.xml", "test-coverage-github": "vendor/bin/phpunit --configuration tests/phpunit/phpunit.xml --coverage-text", "test-coverage-html": "XDEBUG_MODE=coverage vendor/bin/phpunit --configuration tests/phpunit/phpunit.xml --coverage-html=html-coverage", "phpcs": "./vendor/bin/phpcs --standard=phpcs.ruleset.xml src/", "phpcs-test": "./vendor/bin/phpcs --standard=phpcs.ruleset.xml tests/", "phpcbf": "./vendor/bin/phpcbf --standard=phpcs.ruleset.xml src/", "phpcbf-test": "./vendor/bin/phpcbf --standard=phpcs.ruleset.xml tests/"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}