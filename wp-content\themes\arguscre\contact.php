<?php

/**
 * Template Name: Contact
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$order_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
	while (have_posts()) : the_post();
	$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
<div class="container-fluid mainBanner" style="background: url('<?php echo $image[0]; ?>') center center no-repeat; background-size: cover;">
	<div class="container-xl">
		<div class="row mainBannerText">
			<div class="col-12">
				<h2>Contact</h2>
				<div class="breadcrumb">
					<a href="#">Home</a>
					<span><i class="fa-solid fa-angles-right mx-2"></i>Contact</span>
				</div>
			</div>
		</div>
	</div>
</div>
<?php
	endwhile;
}
?>

<div class="cAddSec1 py-5 d-flex align-items-center">
	<div class="container-xl">
		<div class="row gy-4 align-items-center">
			<div class="col-lg-5 col-md-5 col-12">
				<div class="cAddSec1LeftCol">
					<h4>GET IN TOUCH</h4>
					<h2>Message Us</h2>
					<p>Would you like to know more about Steak and Ale restaurants, locations, and franchise opportunities? Please send us a detailed message.</p>
				</div>
			</div>
			<div class="col-lg-1 col-md-1 col-12"></div>
			<div class="col-lg-6 col-md-6 col-12">
				<div class="cAddSec1RightCol">
					<?php echo do_shortcode('[contact-form-7 id="63d1e7b" title="Contact form 1"]') ?>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="cAddSec2 pb-5 d-flex align-items-center">
	<div class="container-xl cAddSec2Container">
		<div class="row gy-4 align-items-center justify-content-center">
			<div class="col-lg-4 col-md-6 col-12">
				<ul class="contact-details__info">
					<li>
						<div class="icon"><img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/phone.svg" alt=""></div>
						<div class="text">
							<h6>Have a question?</h6><a href="tel:<?php echo $order_meta['phone']['value']?>"><?php echo $order_meta['phone']['value']?></a>
						</div>
					</li>
				</ul>
			</div>
			<div class="col-lg-4 col-md-6 col-12">
				<ul class="contact-details__info">
					<li>
						<div class="icon"><img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/email.svg" alt=""></div>
						<div class="text">
							<h6>Send us a message</h6><a href="mailto:<?php echo $order_meta['phone']['value']?>"><?php echo $order_meta['email']['value']?></a>
						</div>
					</li>
				</ul>
			</div>
			<div class="col-lg-4 col-md-6 col-12">
				<ul class="contact-details__info">
					<li>
						<div class="icon"><img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/location.svg" alt=""></div>
						<div class="text">
							<h6>Address</h6><span><?php echo $order_meta['address']['value']?></span>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</div>
</div>

<div class="contactAddSec2">
	<div class="row">
		<div class="col-lg-12">
			<div class="contactAddSec2Inner">
				<div class="map">
					<div id="map"></div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php get_footer(); ?>

<!--/ mapbox /-->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A==" crossorigin="" />
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js" integrity="sha512-XQoYMqMTK8LvdxXYG3nZ448hOEQiglfqkJs1NOQV44cWnUrBc8PkAOcXy20w0vlaXaVUearIOBhiXZ5V3ynxwA==" crossorigin=""></script>

<!--Leaflet-->
<script>
	var locations = [
		["<h3>947-B East Main St. Lexington SC 29072</h3>", 33.974555268505185, -81.22211872144314],
	];

	var LeafIcon = L.Icon.extend({
		options: {
			iconSize: [46, 54],
			iconAnchor: [23, 54],
			shadowAnchor: [4, 62],
			popupAnchor: [0, -50]
		}
	});

	var greenIcon = new LeafIcon({
		iconUrl: '<?php bloginfo('url'); ?>/wp-content/uploads/2025/03/mappin.png',
	})

	var map = L.map('map').setView([33.974555268505185, -81.22211872144314], 6);
	map.attributionControl.setPrefix('');
	// map.scrollWheelZoom.disable();
	// map.doubleClickZoom.disable();

	mapLink = '<a href="http://focusedcre.com/">FocusedCRE</a>';
	//mapBox = L.tileLayer('https://api.mapbox.com/styles/v1/station3/ckqgsuciu00nx17pmxp3sd46m/tiles/256/{z}/{x}/{y}?access_token=pk.eyJ1Ijoic3RhdGlvbjMiLCJhIjoiY2tveDBic2VhMDlwdDMwbWpndDlqY2I4aSJ9.875OSbiQgcgwCel4oJktbg', {}).addTo(map);

	googleStreets = L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
		maxZoom: 11,
		attribution: '&copy; ' + mapLink + ' Contributors',
		subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
	}).addTo(map);

	var markers = [];
	var bounds = L.latLngBounds();

	for (var i = 0; i < locations.length; i++) {
		markers.push([locations[i][1], locations[i][2]]);
		marker = new L.marker([locations[i][1], locations[i][2]], {
			icon: greenIcon
		})
			.bindPopup(locations[i][0])
			.addTo(map);
	}
</script>