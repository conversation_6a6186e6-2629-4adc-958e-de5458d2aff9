<?php

/**
 * Template Name: Properties
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
        <div class="container-fluid mainBanner" style="background: url('<?php echo $image[0]; ?>') center center no-repeat; background-size: cover;">
            <div class="container-xl">
                <div class="row mainBannerText">
                    <div class="col-12">
                        <h2>Our Properties</h2>
                        <p>A powerful, customizable property database plugin built for commercial real estate professionals.<br> Search, filter, and showcase your listings—all from your own website</p>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
}
?>

<?php the_content() ?>

<?php get_footer(); ?>