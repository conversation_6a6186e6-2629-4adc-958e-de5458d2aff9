<?php

/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package WordPress
 * @subpackage Twenty_Twenty_One
 * @since Twenty Twenty-One 1.0
 */

get_header();
$home_meta = get_field_objects(2);
?>

<?php 
if (have_posts()) {
	while (have_posts()) : the_post(); 
	$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail'); 
	$event_date = get_field("event_date");
?>
 
		<div class="container-fluid bottomHeader">
			<div class="container-xl"> 
				<div class="row pt-5 pb-5 align-items-center">
						<div class="col-md-8 col-12">

							<div class="singleEventsText">
								<h1><?php the_title(); ?></h1> 
								<h4><i class="fa-solid fa-calendar-days"></i> <?php echo $event_date; ?></h4>
								
								<?php the_content(); ?>
							</div>

						</div>
						<div class="col-md-4 col-12">

							<div class="singleEventsImg">
								<img src="<?php echo $image[0]; ?>">
							</div>

						</div>
				</div>
			</div>
		</div>

<?php
	endwhile;
}
?>

<?php get_footer();?>
 