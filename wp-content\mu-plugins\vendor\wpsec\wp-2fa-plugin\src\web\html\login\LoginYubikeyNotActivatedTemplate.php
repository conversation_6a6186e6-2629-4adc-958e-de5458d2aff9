<?php

namespace Wpsec\twofa\web\html\login;

/**
 * <PERSON>gin YubiKey Not Activated Template.
 *
 * @package Wpsec
 * @subpackage Wpsec/web/html/login
 */

class LoginYubikeyNotActivatedTemplate {


	/**
	 * Render login yubikey template.
	 *
	 * @since 1.0.0
	 */
	public static function render() {       ?>
		<div id="wpsec_2fa_yubikey_first_dialog_content" class="ui-dialog-content ui-widget-content">
			<div id="wpsec_2fa_login_vertical_line"></div>

			<p class="wpsec-2fa-text-align-center">
				<?php
				/* translators: %s: search term */
				printf( __( 'Select %1$sNext%2$s to set up YubiKey', 'wpsec-wp-2fa' ), '<strong>', '</strong>' );
				?>
			</p>

			<div id="wpsec_2fa_login_mail_first_check_conf">
				<div class="wpsec-2fa-display-flex one_button_only">
					<div class="wpsec_2fa_admin_progress_bar">
						<span class="wpsec_2fa_dot wpsec_2fa_dot_active wpsec_2fa_login_dot"></span>
						<span class="wpsec_2fa_dot"></span>
						<span class="wpsec_2fa_dot"></span>
						<span class="wpsec_2fa_dot"></span>
					</div>
					<button type="button"
							class="file-editor-warning-dismiss button button-primary wpsec_2fa_login_button_next">
						<?php echo __( 'Next', 'wpsec-wp-2fa' ); ?>
					</button>
				</div>
			</div>
		</div>
		<div id="wpsec_2fa_yubikey_scan_dialog_content" class="wpsec-2fa-hidden ui-dialog-content ui-widget-content">
			<p class="wpsec_2fa_paragraf_style">

				<?php
				/* translators: %s: search term */
				printf( __( 'Get your YubiKey, but don’t connect it to your device yet. Select %1$sNext%2$s when ready.', 'wpsec-wp-2fa' ), '<strong>', '</strong>' );
				?>
			</p>
			<div class="wpsec-2fa-display-flex">
				<svg width="130" height="73" viewBox="0 0 130 73" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path fill-rule="evenodd" clip-rule="evenodd" d="M26.9542 15.6253V61.0647L26.9715 65.9329V67.6218C26.9715 69.4872 25.4258 70.9995 23.5192 70.9995H5.48108C3.57444 70.9995 2.02881 69.4872 2.02881 67.6218V15.6253C2.02881 13.7598 3.57444 12.2476 5.48108 12.2476H23.5019C25.4086 12.2476 26.9542 13.7598 26.9542 15.6253ZM14.5864 64.1681C16.7314 64.1681 18.4702 62.4668 18.4702 60.3682L18.4616 60.3597C18.4569 58.2676 16.7247 56.5728 14.5864 56.5682C12.4415 56.5682 10.7026 58.2695 10.7026 60.3682C10.7026 62.4668 12.4415 64.1681 14.5864 64.1681Z" fill="#2B2B2B"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M0 15.6256C0 12.6408 2.46872 10.2476 5.48094 10.2476H23.5018C26.514 10.2476 28.9827 12.6408 28.9827 15.6256V61.065L29 65.9332L29 67.6221C29 70.6068 26.5313 73.0001 23.5191 73.0001H5.48094C2.46872 73.0001 0 70.6068 0 67.6221V15.6256ZM5.48094 14.2482C4.67988 14.2482 4.05734 14.8794 4.05734 15.6256V67.6221C4.05734 68.3683 4.67988 68.9995 5.48094 68.9995H23.5191C24.3201 68.9995 24.9427 68.3683 24.9427 67.6221V65.9332L24.9254 61.072L24.9254 61.065V15.6256C24.9254 14.8794 24.3028 14.2482 23.5018 14.2482H5.48094ZM14.5838 58.5688C13.5456 58.5701 12.7312 59.3898 12.7312 60.3685C12.7312 61.3478 13.5469 62.1681 14.5863 62.1681C15.582 62.1681 16.3725 61.4153 16.4372 60.491C16.4344 60.449 16.4329 60.4068 16.4328 60.3645C16.4306 59.3888 15.619 58.5721 14.5838 58.5688ZM8.67382 60.3685C8.67382 57.1505 11.3358 54.5682 14.5863 54.5682H14.5908C17.7589 54.5751 20.3643 57.0343 20.4857 60.1419C20.4944 60.2168 20.4988 60.2924 20.4988 60.3685C20.4988 63.5864 17.8368 66.1687 14.5863 66.1687C11.3358 66.1687 8.67382 63.5864 8.67382 60.3685Z" fill="black"/>
					<path d="M6.87842 12.2476L6.87842 2.00012H21.4283V12.2476H6.87842Z" fill="#1A1A1A"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M6.87828 14.248C5.75788 14.248 4.84961 13.3525 4.84961 12.2477L4.84961 2.00031C4.84961 0.895576 5.75788 1.16099e-05 6.87828 1.16099e-05L21.4282 1.16099e-05C22.5486 1.16099e-05 23.4568 0.895576 23.4568 2.00031V12.2477C23.4568 13.3525 22.5486 14.248 21.4282 14.248L6.87828 14.248ZM8.90695 10.2474H19.3995V4.00061H8.90695V10.2474Z" fill="black"/>
					<path d="M9.12891 11.1982V3.00029H11.9003V11.1982H9.12891Z" fill="#FED317"/>
					<path d="M13.1865 11.1982V3.00029H19.4222V11.1982H13.1865Z" fill="#FED317"/>
					<path d="M21.4283 33.7674C21.4283 37.7291 18.1712 40.9407 14.1534 40.9407C10.1355 40.9407 6.87842 37.7291 6.87842 33.7674C6.87842 29.8058 10.1355 26.5942 14.1534 26.5942C18.1712 26.5942 21.4283 29.8058 21.4283 33.7674Z" fill="#FED317"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M115.73 10.6628L125.886 36.6998H125.881C130.464 46.9229 127.458 58.9369 118.591 65.83C109.724 72.7232 97.2765 72.7232 88.4097 65.83C79.5429 58.9369 76.5364 46.9229 81.1198 36.6998L91.2902 10.6876C93.0244 5.66145 97.6876 2.21345 103.026 2.00998C108.365 1.80651 113.28 4.88947 115.396 9.76892V9.80865C115.515 10.0917 115.63 10.3748 115.73 10.6628ZM99.3562 15.1741C100.052 16.8442 101.69 17.9332 103.508 17.9332C105.989 17.9332 108.001 15.9321 108.001 13.4637C108.001 11.656 106.906 10.0263 105.227 9.3345C103.548 8.64272 101.615 9.0251 100.33 10.3034C99.0451 11.5816 98.6606 13.504 99.3562 15.1741Z" fill="#2B2B2B"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M102.95 0.0109365C109.101 -0.223482 114.776 3.32818 117.223 8.97059C117.259 9.05445 117.29 9.14051 117.314 9.22815C117.41 9.45998 117.508 9.70807 117.599 9.96735L127.741 35.9705C127.746 35.9834 127.751 35.9963 127.756 36.0092C132.636 47.0518 129.364 59.9846 119.811 67.4112C110.226 74.8629 96.7742 74.8629 87.1889 67.4112C77.6113 59.9655 74.3473 46.9854 79.2816 35.9243L89.4203 9.99324C91.437 4.20111 96.8135 0.244833 102.95 0.0109365ZM123.979 37.2996L113.874 11.3917C113.865 11.368 113.856 11.344 113.847 11.3199C113.769 11.0941 113.674 10.8584 113.561 10.5906C113.532 10.5231 113.508 10.4542 113.487 10.3843C111.659 6.37327 107.561 3.83869 103.102 4.00865C98.5754 4.18116 94.6359 7.10333 93.1735 11.342C93.1647 11.3675 93.1553 11.3929 93.1455 11.4181L82.9751 37.4303C82.9632 37.4606 82.9506 37.4907 82.9373 37.5204C78.734 46.8955 81.4872 57.9181 89.6301 64.2485C97.7784 70.583 109.222 70.583 117.37 64.2485C125.513 57.9181 128.266 46.8955 124.063 37.5204C124.03 37.448 124.002 37.3743 123.979 37.2996ZM104.47 11.1848C103.529 10.7968 102.447 11.0136 101.733 11.7239C101.022 12.4311 100.814 13.4872 101.195 14.4026L99.356 15.1739L101.195 14.4026C101.577 15.3209 102.486 15.9327 103.507 15.9327C104.905 15.9327 106.008 14.8104 106.008 13.4635C106.008 12.4754 105.409 11.5715 104.47 11.1848ZM98.927 8.88244C100.783 7.03622 103.567 6.48821 105.984 7.48388C108.403 8.48066 109.994 10.8363 109.994 13.4635C109.994 17.0535 107.073 19.9333 103.507 19.9333C100.894 19.9333 98.5255 18.3672 97.5169 15.9453C96.5071 13.5204 97.0677 10.7317 98.927 8.88244Z" fill="black"/>
					<path d="M117.044 47.007C117.044 54.7401 110.798 61.0091 103.092 61.0091C95.3863 61.0091 89.1396 54.7401 89.1396 47.007C89.1396 39.2738 95.3863 33.0049 103.092 33.0049C110.798 33.0049 117.044 39.2738 117.044 47.007Z" fill="black"/>
					<path d="M113.057 47.0074C113.057 52.531 108.596 57.0089 103.091 57.0089C97.5874 57.0089 93.1255 52.531 93.1255 47.0074C93.1255 41.4837 97.5874 37.0059 103.091 37.0059C108.596 37.0059 113.057 41.4837 113.057 47.0074Z" fill="#FED317"/>
				</svg>
			</div>
			<br>
			<div class="wpsec-2fa-display-flex">
				<button type="button" id='wpsec_2fa_yubikey_scan_close_button' class="file-editor-warning-dismiss button button-secondary wpsec_2fa_login_button_back">
					<?php echo __( 'Back', 'wpsec-wp-2fa' ); ?>
				</button>
				<p class="wpsec_2fa_admin_progress_bar">
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot"></span>
					<span class="wpsec_2fa_dot"></span>
				</p>
				<button type="button" id="wpsec_2fa_login_button"
						class="file-editor-warning-dismiss button button-primary wpsec_2fa_login_button_next">
					<?php echo __( 'Next', 'wpsec-wp-2fa' ); ?>
				</button>
			</div>
		</div>

		<div id="wpsec_2fa_yubikey_scan_dialog_content" class="wpsec-2fa-hidden ui-dialog-content ui-widget-content">
			<p class="wpsec_2fa_paragraf_style">

				<?php echo __( 'Use Bluetooth to connect your device to the YubiKey, or insert it into your device’s USB port or adaptor.', 'wpsec-wp-2fa' ); ?>
			</p>
			<div class="wpsec-2fa-display-flex">
				<svg width="150" height="72" viewBox="0 0 150 72" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M40.3096 70.027H30.2983C27.3651 70.027 24.9873 67.6803 24.9873 64.7856V17.2272C24.9873 14.3325 27.3651 11.9858 30.2983 11.9858H111.77C114.703 11.9858 117.081 14.3325 117.081 17.2272V64.7856C117.081 67.6803 114.703 70.027 111.77 70.027H40.3096Z" fill="#E6E6E6"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M30.2984 13.9587C28.4561 13.9587 26.9777 15.4303 26.9777 17.227V64.7855C26.9777 66.5822 28.4561 68.0538 30.2984 68.0538H111.77C113.612 68.0538 115.09 66.5822 115.09 64.7855V17.227C115.09 15.4303 113.612 13.9587 111.77 13.9587H30.2984ZM22.9971 17.227C22.9971 13.2344 26.2744 10.0127 30.2984 10.0127H111.77C115.794 10.0127 119.071 13.2344 119.071 17.227V64.7855C119.071 68.7782 115.794 71.9998 111.77 71.9998H30.2984C26.2744 71.9998 22.9971 68.7782 22.9971 64.7855V17.227Z" fill="black"/>
					<path d="M60.9629 62.5317C58.0297 62.5317 55.6519 60.1851 55.6519 57.2904V48.485C55.6519 45.5903 58.0297 43.2437 60.9629 43.2437H81.0652C82.4737 43.2437 83.8246 43.7959 84.8206 44.7788C85.8166 45.7617 86.3762 47.0949 86.3762 48.485V57.2773C86.3762 60.172 83.9984 62.5186 81.0652 62.5186L60.9629 62.5317Z" fill="#999999" fill-opacity="0.33"/>
					<path d="M111.033 1.96826H113.356C116.271 1.96826 117.785 3.521 116.722 5.42098L114.976 8.55268C113.914 10.4461 110.668 12.0054 107.753 12.0054H34.315C31.4005 12.0054 28.1542 10.4461 27.092 8.55268L25.346 5.42098C24.2838 3.521 25.7974 1.96826 28.7118 1.96826H111.033Z" fill="#E6E6E6"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M27.01 4.31141C27.023 4.34839 27.0452 4.39989 27.0826 4.4668L27.0837 4.46881L28.8272 7.59597C28.8275 7.59656 28.8278 7.59715 28.8281 7.59775C29.0859 8.05575 29.764 8.68603 30.8724 9.21665C31.9447 9.73001 33.1914 10.0371 34.3151 10.0371H107.753C108.877 10.0371 110.124 9.73001 111.196 9.21665C112.306 8.68555 112.984 8.0546 113.241 7.59649C113.241 7.59632 113.241 7.59667 113.241 7.59649L114.985 4.46881L114.986 4.4668C115.023 4.39989 115.045 4.34838 115.058 4.31141C114.899 4.19131 114.403 3.93613 113.357 3.93613H28.7119C27.6658 3.93613 27.1694 4.19131 27.01 4.31141ZM26.9916 4.23891C26.9917 4.23892 26.9918 4.24008 26.9919 4.24238ZM115.077 4.23891C115.077 4.2389 115.077 4.24009 115.077 4.24239ZM24.1101 1.61432C25.2422 0.452939 26.9753 0 28.7119 0H113.357C115.093 0 116.826 0.452939 117.958 1.61432C118.55 2.22165 118.949 3.00385 119.032 3.90056C119.113 4.78177 118.877 5.62656 118.46 6.37277M118.459 6.37478L116.714 9.50446L116.711 9.50899C115.906 10.9443 114.43 12.0394 112.923 12.7611C111.378 13.5008 109.544 13.9733 107.753 13.9733H34.3151C32.5243 13.9733 30.6906 13.5008 29.1456 12.7611C27.6381 12.0394 26.1621 10.9443 25.357 9.50899L25.3544 9.50447L23.6095 6.37478C23.6093 6.37439 23.6091 6.37399 23.6089 6.3736C23.1918 5.62719 22.9556 4.78209 23.0369 3.90056C23.1196 3.00385 23.518 2.22165 24.1101 1.61432" fill="black"/>
					<path d="M32.6016 25.1C32.6016 21.624 35.4442 18.8062 38.9507 18.8062H103.004C106.51 18.8062 109.353 21.624 109.353 25.1V33.53C109.353 37.006 106.51 39.8239 103.004 39.8239H38.9507C35.4442 39.8239 32.6016 37.006 32.6016 33.53V25.1Z" fill="#999999" fill-opacity="0.33"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M145.176 19.0013L147.427 24.734L147.453 24.7275C148.47 26.9754 147.804 29.6176 145.839 31.1336C143.874 32.6497 141.115 32.6497 139.15 31.1336C137.184 29.6176 136.519 26.9754 137.535 24.7275L139.786 19.0144C139.922 18.6097 140.149 18.2403 140.449 17.9334C140.985 17.4013 141.714 17.1021 142.474 17.1021C143.234 17.1021 143.963 17.4013 144.499 17.9334C144.756 18.1851 144.961 18.4832 145.103 18.8113C145.112 18.8322 145.122 18.8524 145.132 18.8724C145.155 18.9151 145.176 18.9567 145.176 19.0013ZM139.813 20.1476C139.813 21.5949 141.001 22.7682 142.468 22.7682V22.7551C143.93 22.7552 145.116 21.5898 145.124 20.1476C145.124 18.7002 143.935 17.5269 142.468 17.5269C141.001 17.5269 139.813 18.7002 139.813 20.1476Z" fill="black"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M139.063 16.5479C139.979 15.6389 141.222 15.1294 142.516 15.1294C143.809 15.1294 145.05 15.6373 145.965 16.5437C146.394 16.9653 146.739 17.4629 146.982 18.0108C147.004 18.0533 147.051 18.1475 147.091 18.254C147.112 18.3073 147.135 18.3729 147.156 18.4493L149.244 23.7677C149.283 23.8327 149.318 23.9005 149.35 23.9709C150.751 27.0683 149.831 30.7065 147.129 32.7911C144.43 34.8735 140.643 34.8735 137.943 32.7911C135.255 30.7173 134.331 27.1058 135.701 24.0189L137.932 18.3546C138.166 17.686 138.546 17.0763 139.043 16.5681L139.063 16.5479ZM139.982 24.0618L139.413 25.5043C139.401 25.5345 139.389 25.5645 139.375 25.5941C138.731 27.0181 139.151 28.6944 140.402 29.6592C141.655 30.6261 143.417 30.6261 144.67 29.6592C145.852 28.7478 146.292 27.2016 145.794 25.8329C145.731 25.7315 145.677 25.6232 145.632 25.5087L145.054 24.0385C144.401 24.4583 143.636 24.721 142.815 24.7735C142.716 24.7885 142.614 24.7964 142.51 24.7964C141.58 24.7964 140.711 24.5269 139.982 24.0618ZM142.393 20.8181C142.074 20.7638 141.841 20.4907 141.841 20.1765C141.841 19.8251 142.132 19.5252 142.51 19.5252C142.886 19.5252 143.176 19.8223 143.179 20.1715C143.174 20.5196 142.885 20.8147 142.51 20.8147C142.471 20.8147 142.432 20.8158 142.393 20.8181Z" fill="black"/>
					<path d="M145.668 26.6022C145.668 28.3463 144.235 29.7601 142.468 29.7601C140.7 29.7601 139.268 28.3463 139.268 26.6022C139.268 24.8582 140.7 23.4443 142.468 23.4443C144.235 23.4443 145.668 24.8582 145.668 26.6022Z" fill="#FED317"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M1.99023 24.6957V17.1089H25.06V24.6957H1.99023ZM2.8333 20.9021C2.8333 22.3495 4.02222 23.5228 5.48882 23.5228C6.95542 23.5228 8.14434 22.3495 8.14434 20.9021C8.14434 19.4548 6.95542 18.2814 5.48882 18.2814C4.02222 18.2814 2.8333 19.4548 2.8333 20.9021Z" fill="black"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M0 17.1092C0 16.0196 0.891098 15.1362 1.99032 15.1362H25.0601C26.1594 15.1362 26.2188 16.0196 26.2188 17.1092V24.696C26.2188 25.7857 26.1594 26.669 25.0601 26.669H1.99032C0.891098 26.669 0 25.7857 0 24.696V17.1092ZM9.75591 22.723H23.0698V19.0822H9.75608C9.99965 19.6398 10.1348 20.2551 10.1348 20.9025C10.1348 21.55 9.99959 22.1654 9.75591 22.723ZM5.48891 20.2548C5.11314 20.2548 4.82372 20.5531 4.82372 20.9025C4.82372 21.2518 5.11314 21.5501 5.48891 21.5501C5.86468 21.5501 6.1541 21.2518 6.1541 20.9025C6.1541 20.5531 5.86468 20.2548 5.48891 20.2548Z" fill="black"/>
					<path d="M117.027 17.1084H124.881V24.6952H117.027V17.1084Z" fill="black"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M115.312 17.1087C115.312 16.0191 115.928 15.1357 117.027 15.1357H124.881C125.98 15.1357 126.871 16.0191 126.871 17.1087V24.6956C126.871 25.7852 125.98 26.6686 124.881 26.6686H117.027C115.928 26.6686 115.312 25.7852 115.312 24.6956V17.1087ZM119.018 19.0817V22.7226H122.891V19.0817H119.018Z" fill="black"/>
					<path d="M17.3457 20.9024C17.3457 22.3498 16.1568 23.5231 14.6902 23.5231C13.2236 23.5231 12.0347 22.3498 12.0347 20.9024C12.0347 19.455 13.2236 18.2817 14.6902 18.2817C16.1568 18.2817 17.3457 19.455 17.3457 20.9024Z" fill="#FED317"/>
					<path d="M140.502 32.4199H144.433V36.2133H140.502V32.4199Z" fill="black"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M139.507 32.4201C139.507 31.8753 139.953 31.4336 140.502 31.4336H144.433C144.982 31.4336 145.428 31.8753 145.428 32.4201V36.2135C145.428 36.7583 144.982 37.2 144.433 37.2H140.502C139.953 37.2 139.507 36.7583 139.507 36.2135V32.4201ZM141.498 33.4066V35.227H143.437V33.4066H141.498Z" fill="black"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M123.886 20.9025C123.886 20.3577 124.332 19.916 124.881 19.916H127.601L127.59 20.9025C127.601 19.9161 127.601 19.9161 127.602 19.9161L127.603 19.9161L127.606 19.9161L127.613 19.9163L127.633 19.9168C127.649 19.9172 127.669 19.918 127.694 19.9192C127.744 19.9216 127.812 19.9259 127.895 19.9335C128.061 19.9487 128.291 19.9775 128.563 20.032C129.103 20.1401 129.835 20.3542 130.575 20.7829C132.118 21.6767 133.551 23.424 133.551 26.6025V50.0705C133.551 50.0826 133.551 50.0947 133.55 50.1067C133.497 51.5337 134.238 52.8774 135.486 53.6085C136.735 54.34 138.289 54.34 139.537 53.6085C140.785 52.8774 141.526 51.5337 141.473 50.1067C141.473 50.0947 141.472 50.0826 141.472 50.0705V36.2137C141.472 35.6689 141.918 35.2272 142.468 35.2272C143.017 35.2272 143.463 35.6689 143.463 36.2137V50.0532C143.535 52.1992 142.418 54.2124 140.55 55.307C138.677 56.4045 136.347 56.4045 134.474 55.307C132.605 54.2124 131.488 52.1992 131.561 50.0532V26.6025C131.561 24.1334 130.511 23.0307 129.571 22.4864C129.07 22.1961 128.56 22.0441 128.169 21.9659C127.976 21.9272 127.817 21.9078 127.712 21.8981C127.659 21.8933 127.62 21.8909 127.597 21.8898L127.578 21.889H124.881C124.332 21.889 123.886 21.4473 123.886 20.9025Z" fill="black"/>
				</svg>
			</div>
			<br>
			<div class="wpsec-2fa-display-flex">
				<button type="button" id='wpsec_2fa_yubikey_scan_close_button' class="file-editor-warning-dismiss button button-secondary wpsec_2fa_login_button_back">
					<?php echo __( 'Back', 'wpsec-wp-2fa' ); ?>
				</button>
				<p class="wpsec_2fa_admin_progress_bar">
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot"></span>
				</p>
				<button type="button" id="wpsec_2fa_login_button"
						class="file-editor-warning-dismiss button button-primary wpsec_2fa_login_button_next">
					<?php echo __( 'Next', 'wpsec-wp-2fa' ); ?>
				</button>
			</div>
		</div>

		<div id="wpsec_2fa_yubikey_check_dialog_content" class="wpsec-2fa-hidden ui-dialog-content ui-widget-content">
			<p class="wpsec_2fa_paragraf_style">
				<?php echo __( 'Tap the gold disk or button on your YubiKey. We’ll automatically detect the YubiKey within a few moments.', 'wpsec-wp-2fa' ); ?>
			</p>

			<br>
			<input type="password" autocomplete=new-password
				id="wpsec_2fa_login_yubikey_code_check"
				name="wpsec_2fa_login_yubikey_code_check"
				value="" placeholder="<?php echo __( 'Tap YubiKey...', 'wpsec-wp-2fa' ); ?>">
			<div class="wpsec-2fa-display-flex">
				<button type="button" id='wpsec_2fa_yubikey_back_button' class="file-editor-warning-dismiss button button-secondary wpsec_2fa_login_button_back">
					<?php echo __( 'Back', 'wpsec-wp-2fa' ); ?>
				</button>
				<p class="wpsec_2fa_admin_progress_bar">
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
				</p>
				<input type="button" id="wp_wpsec_2fa_login_yubikey_submit" class="button button-primary button-large"
					value="<?php echo __( 'Verify', 'wpsec-wp-2fa' ); ?>">
			</div>
		</div>
		<input type="hidden" id="wpsec_2fa_current_slide" value="0" />
		<?php
	}
}
