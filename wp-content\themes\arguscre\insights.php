<?php

/**
 * Template Name: Insights
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
	while (have_posts()) : the_post();
		$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
		<div class="container-fluid mainBanner" style="background: url('<?php echo $image[0]; ?>') center center no-repeat; background-size: cover;">
			<div class="container-xl">
				<div class="row mainBannerText">
					<div class="col-md-12">
						<h2>Insights</h2>
						<div class="breadcrumb">
							<a href="#">Home</a>
							<span><i class="fa-solid fa-angles-right mx-2"></i>Insights</span>
						</div>
					</div>
				</div>
			</div>
		</div>
<?php
	endwhile;
}
?>

<div class="container-fluid py-7">
    <div class="container-xl">
        <div class="row">
            <div class="col-12">
                <h2>Launching Soon</h2>
            </div>
        </div>
    </div>
</div>


<?php get_footer(); ?>
