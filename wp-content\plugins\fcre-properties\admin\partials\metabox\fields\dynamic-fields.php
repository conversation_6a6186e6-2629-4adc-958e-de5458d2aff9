<?php
/**
 * Dynamic Multiselect Field
 *
 * This function generates a dynamic multiselect field for use in WordPress.
 *
 * @param WP_Post $post The current post object.
 * @param array   $args Optional. An array of arguments to customize the field.
 */
function fcre_dynamic_multiselect($post, $args = []) {
    $defaults = [
        'option_key'  => '',
        'meta_key'    => '',
        'field_name'  => '',
        'label'       => '',
        'placeholder' => 'Select',
        'group_class' => '',
    ];
    $args = wp_parse_args($args, $defaults);

    if (empty($args['option_key']) || empty($args['meta_key']) || empty($args['field_name'])) {
        echo 'Missing required parameters.';
        return;
    }

    $options = get_option($args['option_key']);
    $selected = get_post_meta($post->ID, $args['meta_key'], true);
    $selected_values = is_array($selected) ? $selected : (array) $selected;

    $uniqid = uniqid('fcre-');

    ?>
    <div class="fcre-multiselect-wrapper">
        <label class="fcre-filter-label"><?php echo esc_html($args['label']); ?></label>
        <div class="filter-select">
            <span class="filter-placeholder <?php echo esc_attr($uniqid); ?>-placeholder"><?php echo esc_html($args['placeholder']); ?></span>
            <span class="filter-select-arrow"></span>
            <span class="filter-onclick"></span>
            <div class="filter-dropdown fcre-hide">
                <div class="filter-dropdown-area">
                    <input id="<?php echo $uniqid; ?>-all" type="checkbox" class="fcre-select-all" data-group=".<?php echo esc_attr($uniqid); ?>-items">
                    <label for="<?php echo $uniqid; ?>-all" class="text-black">All</label><br>
                    <ul class="fcre-dropdown">
                        <?php
                        if ($options) {
                            foreach ($options as $index => $option) {
                                ?>
                                <li>
                                    <input
                                        type="checkbox"
                                        class="<?php echo esc_attr($uniqid); ?>-items"
                                        data-name="<?php echo esc_attr($option['name']); ?>"
                                        name="<?php echo esc_attr($args['field_name']); ?>[]"
                                        id="<?php echo $uniqid; ?>-<?php echo $index; ?>"
                                        value="<?php echo esc_attr($option['id']); ?>"
                                        <?php checked(in_array($option['id'], $selected_values)); ?>
                                    >
                                    <label for="<?php echo $uniqid; ?>-<?php echo $index; ?>" class="text-black">
                                        <?php echo esc_html($option['name']); ?>
                                    </label>
                                </li>
                                <?php
                            }
                        }
                        ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
    jQuery(function($){
    var placeholder = $('.<?php echo $uniqid; ?>-placeholder');
    var groupSelector = '.<?php echo $uniqid; ?>-items';
    var selectAll = $('#<?php echo $uniqid; ?>-all');

    $('.filter-onclick').off('click').on('click', function (e) {
    e.stopPropagation();

    var dropdown = $(this).next('.filter-dropdown');
        $('.filter-dropdown').not(dropdown).addClass('fcre-hide');
        dropdown.toggleClass('fcre-hide');
    });

    $(document).off('click').on('click', function (event) {
            if (!$(event.target).closest(".filter-select, .filter-dropdown").length) {
                $("body").find(".filter-dropdown").addClass("fcre-hide");
            }
    });

    selectAll.change(function() {
        $(groupSelector).prop('checked', $(this).prop('checked'));
        updatePlaceholder();
    });

    $(groupSelector).change(function() {
        selectAll.prop('checked', $(groupSelector + ':checked').length === $(groupSelector).length);
        updatePlaceholder();
    });

    function updatePlaceholder() {
        var selected = [];
        $(groupSelector + ':checked').each(function(){
            selected.push($(this).data('name'));
        });

        if (selected.length === 0) {
            placeholder.text('<?php echo esc_js($args['placeholder']); ?>');
        } else if (selected.length === $(groupSelector).length) {
            placeholder.text('All');
        } else {
            placeholder.text(selected.join(', '));
        }
    }

    updatePlaceholder();
});
    </script>
    <?php
}

/**
 * Dynamic Single Select Field
 *
 * @param WP_Post $post The current post object.
 * @param array   $args Optional. An array of arguments to customize the field.
 */
function fcre_dynamic_select($post, $args = []) {
    $defaults = [
        'option_key'  => '',
        'meta_key'    => '',
        'field_name'  => '',
        'label'       => '',
        'placeholder' => 'Select',
        'group_class' => '',
    ];
    $args = wp_parse_args($args, $defaults);

    if (empty($args['option_key']) || empty($args['meta_key']) || empty($args['field_name'])) {
        echo 'Missing required parameters.';
        return;
    }

    $options = get_option($args['option_key']);
    $selected = get_post_meta($post->ID, $args['meta_key'], true);

    ?>
    <label class="fcre-select-label"><?php echo esc_html($args['label']); ?></label>
    <select name="<?php echo esc_attr($args['field_name']); ?>" class="fcre-select">
        <option value=""><?php echo esc_html($args['placeholder']); ?></option>
        <?php
        if (is_array($options)) {
            foreach ($options as $option) {
                $id   = isset($option['id']) ? $option['id'] : '';
                $name = isset($option['name']) ? $option['name'] : '';
                ?>
                <option value="<?php echo esc_attr($id); ?>" <?php selected($selected, $id); ?>>
                    <?php echo esc_html($name); ?>
                </option>
                <?php
            }
        }
        ?>
    </select>
    <?php
}



/**
 * Render a File Gallery Field.
 * 
 * @param string $meta_key Meta key for saving the gallery
 */
function fcre_file_gallery($meta_key) {
    global $post;
    $files_gallery = get_post_meta($post->ID, $meta_key, true);
    $unique_id = 'fcre-gallery-' . sanitize_key($meta_key); // Unique ID for each gallery
    ?>
    <div class="form-table property-details fcre-file-gallery" id="<?= esc_attr($unique_id); ?>" data-meta-key="<?= esc_attr($meta_key); ?>">
        <ul class="fcre-files-gallery-metabox-list">
            <?php if ($files_gallery) : ?>
                <?php foreach ($files_gallery as $i => $file) :
                    $file_url = wp_get_attachment_url($file['file']);
                    $file_name = basename(get_attached_file($file['file']));
                    $file_type = wp_check_filetype($file_url);
                ?>
                    <li>
                        <input type="hidden" name="<?= esc_attr($meta_key); ?>[<?= $i; ?>][file]" value="<?= esc_attr($file['file']); ?>">
                        <a href="<?= esc_url($file_url); ?>" class="file-link <?= esc_attr($file_type['ext']); ?>" target="_blank"><?= esc_html($file_name); ?></a>
                        <a href="#" class="change-file file-edit" data-uploader-title="Change File" data-uploader-button-text="Change File"></a>
                        <a href="#" class="remove-file file-remove"></a>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ul>

        <a href="#" class="file-gallery-add button button-primary" data-uploader-title="Add Files to Gallery" data-uploader-button-text="Add Files">Add Files</a>
    </div>
    <?php
}

function fcre_photo_gallery($meta_key, $btn_label = 'Add Images'){
    global $post;
    $gallery_photos = get_post_meta($post->ID, $meta_key, true);
    $unique_id = 'fcre-photo-' . sanitize_key($meta_key); // Unique ID for each gallery
?>

<div id="<?= esc_attr($unique_id); ?>" class="fcre-photo-gallery" data-meta-key="<?= esc_attr($meta_key); ?>">
    <ul class="fcre-photo-gallery-metabox-list">
        <?php

        if (!empty($gallery_photos)) :
            foreach ($gallery_photos as $i => $photo) :
                $photo_url = wp_get_attachment_image_url($photo['id'], 'thumbnail');
                $caption = isset($photo['caption']) ? $photo['caption'] : '';
        ?>
                <li>
                    <input type="hidden" name="<?= esc_attr($meta_key); ?>[<?php echo esc_attr($i); ?>][id]" value="<?php echo esc_attr($photo['id']); ?>">
                    <img src="<?php echo esc_url($photo_url); ?>" alt="">
                    <input type="text" class="photo-caption" name="<?= esc_attr($meta_key); ?>[<?php echo esc_attr($i); ?>][caption]" value="<?php echo esc_attr($caption); ?>" placeholder="Enter caption..." />
                    <a href="#" class="change-photo photo-edit" data-uploader-title="Change Image" data-uploader-button-text="Change Image"></a>
                    <a href="#" class="remove-photo photo-remove"></a>
                </li>
        <?php endforeach; endif; ?>
    </ul>

    <a href="#" class="photo-gallery-add button button-primary" data-uploader-title="<?=$btn_label?>" data-uploader-button-text="<?=$btn_label?>"><?=$btn_label?></a>
</div>


<?php
}


/**
 * Render an advanced repeater field
 * 
 * @param string $meta_key Unique meta key
 * @param array $fields Fields definitions
 * @param array $values Optional saved values
 */
function render_fcre_advanced_repeater($meta_key, $fields, $values = []) {
    if (empty($values)) {
        global $post;
        $values = get_post_meta($post->ID, $meta_key, true);
        if (!is_array($values)) {
            $values = [];
        }
    }
    ?>
    <div class="fcre-repeater-wrap" id="wrap-<?php echo esc_attr($meta_key); ?>">
        <div class="fcre-repeater-body" 
             id="<?php echo esc_attr($meta_key); ?>" 
             data-meta-key="<?php echo esc_attr($meta_key); ?>" 
             data-fields='<?php echo json_encode($fields); ?>'>
            
            <?php if (!empty($values)) : ?>
                <?php foreach ($values as $index => $item) : ?>
                    <div class="fcre-repeater-item">
                        <div class="fcre-repeater-drag">☰</div>
                        <div class="fcre-repeater-fields">
                            <?php foreach ($fields as $field) :
                                $field_name = $field['name'];
                                $field_label = $field['label'];
                                $field_type = $field['type'];
                                $field_value = isset($item[$field_name]) ? $item[$field_name] : '';

                                // Render field based on type
                                ?>
                                <div class="fcre-repeater-field">
                                    <label><?php echo esc_html($field_label); ?></label>
                                    <?php if ($field_type === 'text') : ?>
                                        <input type="text" name="<?php echo esc_attr($meta_key); ?>[<?php echo $index; ?>][<?php echo esc_attr($field_name); ?>]" value="<?php echo esc_attr($field_value); ?>" class="form-control">
                                    <?php elseif ($field_type === 'textarea') : ?>
                                        <textarea name="<?php echo esc_attr($meta_key); ?>[<?php echo $index; ?>][<?php echo esc_attr($field_name); ?>]" class="form-control"><?php echo esc_textarea($field_value); ?></textarea>
                                    <?php elseif ($field_type === 'select' && !empty($field['options'])) : ?>
                                        <select name="<?php echo esc_attr($meta_key); ?>[<?php echo $index; ?>][<?php echo esc_attr($field_name); ?>]" class="form-control">
                                            <?php foreach ($field['options'] as $key => $label) : ?>
                                                <option value="<?php echo esc_attr($key); ?>" <?php selected($field_value, $key); ?>><?php echo esc_html($label); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    <?php elseif ($field_type === 'wysiwyg') : ?>
                                        <textarea class="form-control wysiwyg-init" data-name="<?php echo esc_attr($meta_key); ?>[<?php echo $index; ?>][<?php echo esc_attr($field_name); ?>]"><?php echo esc_textarea($field_value); ?></textarea>
                                    <?php elseif ($field_type === 'image') : ?>
                                        <div class="fcre-image-upload">
                                            <input type="hidden" name="<?php echo esc_attr($meta_key); ?>[<?php echo $index; ?>][<?php echo esc_attr($field_name); ?>]" value="<?php echo esc_attr($field_value); ?>">
                                            <button type="button" class="button fcre-upload-button"><?php echo $field_value ? 'Change Image' : 'Upload Image'; ?></button>
                                            <div class="fcre-image-preview"><?php echo $field_value ? wp_get_attachment_image($field_value, 'thumbnail') : ''; ?></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="fcre-repeater-remove"><span class="icon-trash"></span></div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <button type="button" class="button button-primary fcre-repeater-add" data-meta-key="<?php echo esc_attr($meta_key); ?>">Add Row</button>
    </div>
<?php
}


/**
 * Render a Relationship Dual List Field
 *
 * @param string $field_name Meta key
 * @param string $post_type  Post type slug
 * @param array  $args       Additional query args
 * @param string $label      Field Label
 */
function fcre_relationship_dual_field($field_name, $post_type = 'post', $args = [], $label = '') {
    global $post;

    // Get selected post IDs (comma-separated)
    $selected_raw = get_post_meta($post->ID, $field_name, true);
    $selected = array_filter(explode(',', $selected_raw)); // Remove empty values
 
    $default_args = [
        'post_type'      => $post_type,
        'posts_per_page' => -1,
        'post_status'    => 'publish',
        'orderby'        => 'title',
        'order'          => 'ASC',
    ];

    $query_args = wp_parse_args($args, $default_args);
    $related_posts = get_posts($query_args);

    ?>
    <div class="fcre-relationship-wrapper" style="display: flex; gap: 30px;">

        <!-- Available Posts -->
        <div class="fcre-column fcre-available">
            <h4>Available</h4>
            <input type="text" class="fcre-search" placeholder="Search available...">
            <ul class="fcre-list fcre-available-list" data-related="<?= esc_attr($field_name) ?>">
                <?php foreach ($related_posts as $related_post) : ?>
                    <!-- Show posts that are not selected, but disable selected ones -->
                    <?php if (empty($selected) || !in_array($related_post->ID, $selected)) : ?>
                        <li class="fcre-item" data-id="<?= esc_attr($related_post->ID) ?>">
                            <?= esc_html($related_post->post_title) ?>
                        </li>
                    <?php else : ?>
                        <li class="fcre-item fcre-disabled" data-id="<?= esc_attr($related_post->ID) ?>" style="pointer-events: none; opacity: 0.5;">
                            <?= esc_html($related_post->post_title) ?>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </div>

        <!-- Selected Posts -->
        <div class="fcre-column fcre-selected">
            <h4>Selected</h4>
            <ul class="fcre-list fcre-selected-list" data-related="<?= esc_attr($field_name) ?>">
                <?php
                if (!empty($selected)) :
                    foreach ($selected as $post_id) :
                        $selected_related_post = get_post($post_id);
                        if ($selected_related_post) : ?>
                            <li class="fcre-item" data-id="<?= esc_attr($selected_related_post->ID) ?>"><?= esc_html($selected_related_post->post_title) ?> <span class="fcre-remove">×</span></li>
                        <?php endif;
                    endforeach;
                endif;
                ?>
            </ul>

            <!-- Hidden input to save selected IDs -->
            <input type="hidden" name="<?= esc_attr($field_name) ?>" class="fcre-hidden-input" value="<?= implode(',', $selected) ?>">
        </div>

    </div>
    <?php
    wp_reset_postdata();
}

/**
 * File Upload Field
 *
 * @param WP_Post $post The current post object.
 * @param array $args Optional. An array of arguments to customize the field.
 */
function fcre_file_upload_field($post, $args = []) {
    $defaults = [
        'meta_key'    => '',
        'field_name'  => '',
        'label'       => '',
        'placeholder' => 'Upload File',
        'group_class' => '',
    ];
    $args = wp_parse_args($args, $defaults);

    if (empty($args['meta_key']) || empty($args['field_name'])) {
        echo '<div class="notice notice-error">Missing required parameters.</div>';
        return;
    }

    $uniqid = uniqid('fcre-file-');
    $attachment_id = get_post_meta($post->ID, $args['meta_key'], true);
    $label = esc_html($args['label']);
    $placeholder = esc_attr($args['placeholder']);
    $group_class = esc_attr($args['group_class']);

    // Get attachment details if we have an ID
    $file_data = null;
    if ($attachment_id) {
        $attachment = get_post($attachment_id);
        if ($attachment) {
            $file_url = wp_get_attachment_url($attachment_id);
            $file_name = basename(get_attached_file($attachment_id));
            $file_size = size_format(filesize(get_attached_file($attachment_id)));
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            $file_data = [
                'id' => $attachment_id,
                'url' => $file_url,
                'name' => $file_name,
                'size' => $file_size,
                'ext' => $file_ext
            ];
        }
    }
    ?>

    <div class="fcre-file-upload-wrapper <?php echo $group_class; ?>">
        <?php if ($label): ?>
            <label for="<?php echo $uniqid; ?>"><?php echo $label; ?></label>
        <?php endif; ?>

        <input type="hidden" id="<?php echo $uniqid; ?>" name="<?php echo esc_attr($args['field_name']); ?>" value="<?php echo $attachment_id; ?>">

        <div class="fcre-file-uploader <?php echo $file_data ? 'has-file' : 'no-file'; ?>">
            <?php if ($file_data): ?>
                <!-- File Preview with Icon and Details -->
                <div class="fcre-file-preview-container">
                    <div class="fcre-file-icon-wrapper">
                        <a href="<?php echo esc_url($file_data['url']); ?>"
                           class="fcre-file-link <?php echo esc_attr($file_data['ext']); ?>"
                           target="_blank"
                           title="<?php echo esc_attr($file_data['name']); ?>">
                            <?php echo esc_html($file_data['name']); ?>
                        </a>
                    </div>
                    <div class="fcre-file-details">
                        <div class="fcre-file-name"><?php echo esc_html($file_data['name']); ?></div>
                        <div class="fcre-file-size"><?php echo esc_html($file_data['size']); ?></div>
                    </div>
                    <div class="fcre-file-actions">
                        <button type="button" class="button fcre-upload-btn fcre-change-file" title="Change File">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="button fcre-remove-btn fcre-remove-file" title="Remove File">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <!-- Upload Area -->
                <div class="fcre-file-upload-area">
                    <div class="fcre-upload-placeholder">
                        <span class="dashicons dashicons-upload"></span>
                        <p><?php echo $placeholder; ?></p>
                        <button type="button" class="button button-primary fcre-upload-btn">Select File</button>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php
}


/**
 * WYSIWYG Editor Field
 *
 * @param WP_Post $post The current post object.
 * @param array   $args Optional. An array of arguments to customize the field.
 */
function fcre_wysiwyg_field($post, $args = []) {
    $defaults = [
        'meta_key'    => '',
        'field_name'  => '',
        'label'       => '',
        'editor_args' => [], // WP_Editor args
        'group_class' => '',
    ];
    $args = wp_parse_args($args, $defaults);

    if (empty($args['meta_key']) || empty($args['field_name'])) {
        echo '<div class="notice notice-error">Missing required parameters for WYSIWYG field.</div>';
        return;
    }

    $uniqid = uniqid('fcre-wysiwyg-');
    $value  = get_post_meta($post->ID, $args['meta_key'], true);
    $label  = esc_html($args['label']);
    $field_name = esc_attr($args['field_name']);
    $group_class = esc_attr($args['group_class']);

    echo '<div class="fcre-wysiwyg-wrapper ' . $group_class . '">';
    if ($label) {
        echo '<label for="' . $uniqid . '">' . $label . '</label>';
    }

    $editor_args = wp_parse_args($args['editor_args'], [
        'textarea_name' => $field_name,
        'textarea_rows' => 8,
        'media_buttons' => false,
        'teeny'         => true,
    ]);

    wp_editor($value, $uniqid, $editor_args);
    echo '</div>';
}


?>

