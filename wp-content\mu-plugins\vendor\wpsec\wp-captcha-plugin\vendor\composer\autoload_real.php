<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInit915a718651dbe9c9bf619aa70f02c756
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        spl_autoload_register(array('ComposerAutoloaderInit915a718651dbe9c9bf619aa70f02c756', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInit915a718651dbe9c9bf619aa70f02c756', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInit915a718651dbe9c9bf619aa70f02c756::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
