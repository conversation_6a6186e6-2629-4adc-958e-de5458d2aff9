<?php

/**
 * The plugin bootstrap file
 *
 * This file is read by WordPress to generate the plugin information in the plugin
 * admin area. This file also includes all of the dependencies used by the plugin,
 * registers the activation and deactivation functions, and defines a function
 * that starts the plugin.
 *
 * @link              https://focusedcre.com
 * @since             1.0.0
 * @package           Fcre_Properties
 *
 * @wordpress-plugin
 * Plugin Name:       SnapCRE
 * Plugin URI:        https://focusedcre.com
 * Description:       WordPress powered custom properties database system, you can add as many properties you want, commercial or residential types supported. 
 * Version:           1.0.0
 * Author:            Focused CRE
 * Author URI:        https://focusedcre.com/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       fcre-properties
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 * Start at version 1.0.0 and use SemVer - https://semver.org
 * Rename this for your plugin and update it as you release new versions.
 */
define( 'FCRE_PROPERTIES_VERSION', '1.0.0' );

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-fcre-properties-activator.php
 */
function activate_snapcre() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-fcre-properties-activator.php';
	Fcre_Properties_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-fcre-properties-deactivator.php
 */
function deactivate_snapcre() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-fcre-properties-deactivator.php';
	Fcre_Properties_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_snapcre' );
register_deactivation_hook( __FILE__, 'deactivate_snapcre' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-fcre-properties.php';

/**
 * The global plugin file that is used to define global variables,
 */

require plugin_dir_path( __FILE__ ) . 'includes/class-fcre-global.php';

/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * then kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0.0
 */

function run_snapcre() {

	$plugin = new Fcre_Properties();
	$plugin->run();

}
run_snapcre();
