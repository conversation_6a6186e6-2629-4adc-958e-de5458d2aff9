<?php

/**
 * Register Custom Post Types of the plugin.
 *
 * @link       https://focusedcre.com
 * @since      1.0.0
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/admin
 * <AUTHOR> CRE <<EMAIL>>
 */
class Fcre_CPT {

	private $FCRE;
	private $plugin_name;
	private $version;


	public function __construct(  ) {

		$this->FCRE = Fcre_Global::getInstance();
		$this->plugin_name = $this->FCRE->plugin_name;
		$this->version = $this->FCRE->version;

	}
	
	 /**
     * Generate Custom Post of Properties of the plugin.
     *
     * @since     1.0.0
     */

	 public function fcre_init_properties_custom_post()
	 {
		 $labels = array(
			 'name' => _x('Properties', 'post type general name'),
			 'singular_name' => _x('Properties', 'post type singular name'),
			 'add_new' => _x('Add New', 'book'),
			 'add_new_item' => __('Add New Property'),
			 'edit_item' => __('Edit Properties'),
			 'all_items' => __('Properties'),
			 'view_item' => __('View Properties'),
			 'search_items' => __('Search Properties'),
			 'not_found' => __('No Properties found'),
			 'not_found_in_trash' => __('No Properties found in the Trash'),
			 'parent_item_colon' => '',
			 'menu_name' => 'Properties',
		 );
		 $args = array(
			 'labels' => $labels,
			 'hierarchical' => false,
			 'supports' => array('title', 'editor', 'thumbnail'),
			 'public' => true,
			 'show_ui' => true,
			 'show_in_nav_menus' => false,
			 'publicly_queryable' => true,
			 'exclude_from_search' => false,
			 'has_archive' => false,
			 'query_var' => true,
			 'can_export' => true,
			 'capability_type' => 'post',
			 'show_in_menu' => $this->FCRE->plugin_name,
		 );
		 register_post_type($this->FCRE->properties_custom_post_slug, $args);
	 }
 
	 /**
	  * Generate Custom Post of Agents of the plugin.
	  *
	  * @since     1.0.0
	  */
	 public function fcre_init_agents_custom_post()
	 {
		 $labels = array(
			 'name' => _x('Agents', 'post type general name'),
			 'singular_name' => _x('Agent', 'post type singular name'),
			 'add_new' => _x('Add New', 'book'),
			 'add_new_item' => __('Add New Agent'),
			 'edit_item' => __('Edit Agent'),
			 'all_items' => __('Agent'),
			 'view_item' => __('View Agent'),
			 'search_items' => __('Search Agent'),
			 'not_found' => __('No Agent found'),
			 'not_found_in_trash' => __('No Agent found in the Trash'),
			 'parent_item_colon' => '',
			 'menu_name' => 'Agents',
 
		 );
		 $args = array(
			 'labels' => $labels,
			 'hierarchical' => false,
			 'taxonomies'          => array('category'),
			 'supports' => array('title', 'editor', 'thumbnail'),
			 'public' => true,
			 'show_ui' => true,
			 'show_in_nav_menus' => false,
			 'publicly_queryable' => true,
			 'exclude_from_search' => false,
			 'has_archive' => false,
			 'query_var' => true,
			 'can_export' => true,
			 'capability_type' => 'post',
			 'show_in_menu' => $this->FCRE->plugin_name,
		 );
		 register_post_type($this->FCRE->agent_custom_post_slug, $args);
	 }
 
	 /**
	  * Generate Custom Post of Agreements of the plugin.
	  *
	  * @since     1.0.0
	  */
	 public function fcre_init_agreement_custom_post()
	 {
		 $labels = array(
			 'name' => _x('Agreements', 'post type general name'),
			 'singular_name' => _x('Agreement', 'post type singular name'),
			 //'add_new' => _x('Add New', 'Agreement'),
			 //          'add_new_item' => __('Add New Agent'),
			 'edit_item' => __('Edit Agreement'),
			 'all_items' => __('Agreements'),
			 'view_item' => __('View Agreement'),
			 'search_items' => __('Search Agreements'),
			 'not_found' => __('No Agreement found'),
			 'not_found_in_trash' => __('No Agreement found in the Trash'),
			 'parent_item_colon' => '',
			 'menu_name' => 'Agreements',
			 'rewrite' => array('slug' => '/'),
		 );
		 $args = array(
			 'labels' => $labels,
			 'description' => '',
			 'public' => true,
			 'menu_position' => 30,
			 'supports' => array('title'),
			 'has_archive' => false,
			 'capability_type' => 'post',
			 'capabilities' => array(
				 'create_posts' => 'do_not_allow', // false < WP 4.5, credit @Ewout
			 ),
			 'publicly_queryable' => false,
			 'map_meta_cap' => true, // Set to `false`, if users are not allowed to edit/delete existing posts
			 'show_in_menu' => $this->FCRE->plugin_name,
		 );
		 register_post_type($this->FCRE->agreements_custom_post_slug, $args);
	 }

}
