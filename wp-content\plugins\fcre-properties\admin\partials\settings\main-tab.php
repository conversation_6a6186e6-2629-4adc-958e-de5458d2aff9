<?php
if (!defined('ABSPATH')) {
    die('-1');
}

// Define all types you want to manage
$types = [
    'transaction-types' => 'Transaction Types',
    'property-types' => 'Property Types',
    'property-status' => 'Property Status',
    'lease-types' => 'Lease Types',
];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    foreach ($types as $option_key => $label) {
        if (isset($_POST[$option_key])) {
            update_option($this->FCRE->plugin_name . '-' . $option_key, $_POST[$option_key]);
        }
    }
}

// Load saved options
$options = [];
foreach ($types as $option_key => $label) {
    $saved_data = get_option($this->FCRE->plugin_name . '-' . $option_key, []);
    $options[$option_key] = is_array($saved_data) ? $saved_data : [];
}

// Prepare last IDs
$last_ids = [];
foreach ($types as $option_key => $label) {
    $last_ids[$option_key] = !empty($options[$option_key]) ? max(array_column($options[$option_key], 'id')) : 0;
}
?>

<div class="tab-content-header">
    <h2>Manage All Types</h2>
</div>

<?php foreach ($types as $option_key => $label): ?>
    <div class="fcre-settings-card">
    <table class="fcre-types-table fcre-sort-table" id="<?php echo esc_attr($option_key); ?>-table">
        <thead>
        <tr>
            <h3 class="card-title"><?php echo esc_html($label); ?></h3>
            <small>Example: 
                <?php  
                if($option_key == 'transaction-types'){
                    echo 'For Sale, For Lease, etc.';
                }
                if($option_key == 'property-types'){
                    echo 'Office, Retail, Industrial, etc.';
                }
                if($option_key == 'property-status'){
                    echo 'Available, Under Contract, Sold, etc.';
                }
                if($option_key == 'lease-types'){
                    echo 'Net, Gross, Modified Gross, etc.';
                }
                ?>
            </small>
        </tr>
        </thead>
        <tbody id="<?php echo esc_attr($option_key); ?>">
        <?php
        $index = 1;
        if (!empty($options[$option_key])) {
            foreach ($options[$option_key] as $item) {
                ?>
                <tr>
                    <td class="drag-handler" valign="top">
                        <span class="move-handle"></span>
                        <input type="hidden" name="<?php echo $option_key; ?>[<?php echo $index; ?>][id]" value="<?php echo esc_attr($item['id']); ?>">
                        <input type="text" name="<?php echo $option_key; ?>[<?php echo $index; ?>][name]" value="<?php echo esc_attr($item['name']); ?>" placeholder="Enter name">
                    </td>
                    <td><span><?php echo esc_html($item['id']); ?></span></td>
                    <td valign="top" class="row-remove"><span><i class="icon-trash"></i></span></td>
                </tr>
                <?php
                $index++;
            }
        }
        ?>
        </tbody>
    </table>

    <button class="fcre-btn-add-more button button-default" data-type="<?php echo esc_attr($option_key); ?>">+ Add more</button>
    </div>
<?php endforeach; ?>

<script>
    jQuery(function ($) {
        var lastIds = <?php echo json_encode($last_ids); ?>;
        var counts = {};

        <?php foreach ($types as $option_key => $label): ?>
        counts['<?php echo $option_key; ?>'] = $('#<?php echo $option_key; ?> tr').length + 1;

        makeSortable('<?php echo $option_key; ?>');

        <?php endforeach; ?>

        function makeSortable(type) {
            $('#' + type).sortable({
                opacity: 0.6,
                axis: 'y',
                handle: ".drag-handler",
                placeholder: "ui-state-highlight",
                stop: function () {
                    resetIndexes(type);
                },
                helper: function (e, ui) {
                    ui.children().each(function () {
                        $(this).width($(this).width());
                    });
                    return ui;
                }
            });
        }

        function resetIndexes(type) {
            $('#' + type + ' tr').each(function (i) {
                $(this).find('input:hidden').attr('name', type + '[' + i + '][id]');
                $(this).find('input:text').attr('name', type + '[' + i + '][name]');
            });
        }

        $('.fcre-btn-add-more').click(function (e) {
            e.preventDefault();
            var type = $(this).data('type');
            var tbody = $('#' + type);
            var id = ++lastIds[type];
            var index = counts[type]++;

            var html = '<tr>' +
                '<td class="drag-handler" valign="top">' +
                '<span class="move-handle"></span>' +
                '<input type="hidden" name="' + type + '[' + index + '][id]" value="' + id + '">' +
                '<input type="text" name="' + type + '[' + index + '][name]" value="" placeholder="Enter name">' +
                '</td>' +
                '<td><span>' + id + '</span></td>' +
                '<td valign="top" class="row-remove"><span><i class="icon-trash"></i></span></td>' +
                '</tr>';

            tbody.append(html);
        });

        $(document).on('click', '.row-remove', function () {
            if (confirm("Are you sure you want to delete this item? This will be removed from filter options for your users.")) {
                $(this).closest('tr').remove();
            }
        });
    });
</script>
