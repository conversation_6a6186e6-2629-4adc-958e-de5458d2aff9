<?php

/**
 * Template Name: Events
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
        <div class="container-fluid mainBanner" style="background: url('<?php echo $image[0]; ?>') center center no-repeat; background-size: cover;">
            <div class="container-xl">
                <div class="row mainBannerText">
                    <div class="col-12">
                        <h2>Events</h2>
                        <div class="breadcrumb">
                            <a href="#">Home</a>
                            <span><i class="fa-solid fa-angles-right mx-2"></i>Events</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
}
?>

<div class="container-fluid py-5">
    <div class="container-xl">
        <div class="row">
        <?php
        $args = query_posts(
            array(
                'post_type'      => 'events', // This is the name of your CPT
                'order'          => 'DESC',
                'posts_per_page' => -1
            )
        );
        if (have_posts()) {
            while (have_posts()) : the_post();
                $image  = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
				$event_date = get_field("event_date");
        ?>
                <div class="col-lg-4 col-md-6 col-12 px-cus-1">
                    <div class="eventsBox">
                        <div class="eventsImg">
                            <img src="<?php echo $image[0]; ?>">
                        </div>
                        <div class="eventsText">
                            <h3><?php the_title(); ?></h3> 
							<h5><i class="fa-solid fa-calendar-days"></i> <?php echo $event_date; ?></h5>
                            <div class="eventsIcon">
                                <a href="<?php the_permalink(); ?>"><i class="fa-solid fa-plus"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
        <?php
            endwhile;
        }
        wp_reset_query();
        ?>
        </div>
    </div>
</div>

<?php get_footer(); ?>