jQuery(function($) {

    class FCREPhotoGallery {
        constructor(options) {
            this.container = $(options.container);
            this.list = this.container.find('.fcre-photo-gallery-metabox-list');
            this.metaKey = options.metaKey;
            this.fileFrame = null;
    
            this.bindEvents();
            this.makeSortable();
        }
    
        bindEvents() {
            const self = this;
    
            self.container.on('click', '.photo-gallery-add', function (e) {
                e.preventDefault();
                self.openMediaUploader(true, $(this));
            });
    
            self.container.on('click', '.change-photo', function (e) {
                e.preventDefault();
                self.openMediaUploader(false, $(this));
            });
    
            self.container.on('click', '.remove-photo', function (e) {
                e.preventDefault();
                $(this).closest('li').fadeOut(200, function () {
                    $(this).remove();
                    self.resetIndexes();
                });
            });
        }
    
        openMediaUploader(multiple, button) {
            const self = this;
    
            if (self.fileFrame) {
                self.fileFrame.close();
            }
    
            self.fileFrame = wp.media({
                title: button.data('uploader-title'),
                button: {
                    text: button.data('uploader-button-text'),
                },
                multiple: multiple,
                library: {
                    type: 'image'
                }
            });
    
            self.fileFrame.on('select', function () {
                const selection = self.fileFrame.state().get('selection');
                const listIndex = self.list.find('li').length;
    
                if (multiple) {
                    selection.map(function (attachment, i) {
                        self.addPhoto(attachment.toJSON(), listIndex + i);
                    });
                } else {
                    const attachment = selection.first().toJSON();
                    self.updatePhoto(button, attachment);
                }
            });
    
            self.fileFrame.open();
        }
    
        addPhoto(photo, index) {
            const html = `
                <li>
                    <input type="hidden" name="${this.metaKey}[${index}][id]" value="${photo.id}">
                    <img src="${photo.sizes.thumbnail ? photo.sizes.thumbnail.url : photo.url}" alt="" />
                    <input type="text" class="photo-caption" name="${this.metaKey}[${index}][caption]" placeholder="Enter caption..." />
                    <a href="#" class="change-photo photo-edit" data-uploader-title="Change Image" data-uploader-button-text="Change Image"></a>
                    <a href="#" class="remove-photo photo-remove"></a>
                </li>`;
            this.list.append(html);
        }
    
        updatePhoto(button, photo) {
            const parent = button.closest('li');
            parent.find('input:hidden').val(photo.id);
            parent.find('img').attr('src', photo.sizes.thumbnail ? photo.sizes.thumbnail.url : photo.url);
        }
    
        resetIndexes() {
            this.list.find('li').each((i, li) => {
                $(li).find('input:hidden').attr('name', `${this.metaKey}[${i}][id]`);
                $(li).find('input.photo-caption').attr('name', `${this.metaKey}[${i}][caption]`);
            });
        }
    
        makeSortable() {
            const self = this;
            self.list.sortable({
                opacity: 0.6,
                stop: function () {
                    self.resetIndexes();
                }
            });
        }
    }
    
    // Global
    window.FCREPhotoGallery = FCREPhotoGallery;
    
    // Initialize
    $(document).ready(function() {
        $('.fcre-photo-gallery').each(function(){
            new FCREPhotoGallery({
                container: '#' + $(this).attr('id'),
                metaKey: $(this).data('meta-key')
            });
        });
    });
    
    });
    