<?php
if (!defined('ABSPATH')) {
    die('-1');
}
$ca_check = get_post_meta($post->ID, 'ca_check', true);
$is_global_ca_check = get_post_meta($post->ID, 'is_global_ca_check', true);
$confidential_agreement = get_post_meta($post->ID, 'confidential_agreement', true);
$flyer_ca_required = get_post_meta($post->ID, 'flyer_ca_required', true);
$om_ca_required = get_post_meta($post->ID, 'om_ca_required', true);
?>
<table width="100%" class="property-details">
    <tbody>
        <tr>
            <td style="padding-top: 0;">
                <h3>Confidentiality <strong>Agreement</strong></h3>
                <p><input id="ca-check" type="checkbox" name="ca_check" value="y" <?php if ($ca_check == 'y') echo 'checked'; ?>><label style="display: inline;" for="ca-check">This Property is Private - Check this box if you want to enforce your visitors to sign the confidentiality agreement for this property listing.</label></p>
                <!-- <p><input id="is-global-ca-check" type="checkbox" name="is_global_ca_check" value="y" <?php if ($is_global_ca_check == 'y') echo 'checked'; ?>><label style="display: inline;" for="is-global-ca-check">Use global agreement content. <a href="<?=admin_url('admin.php?page=fcre-properties-settings&tab=fcre-confidentiality-agreement')?>">Click</a> here to edit global agreement content</label></p> -->

                <!-- Conditional checkboxes that appear when main CA checkbox is checked -->
                <div id="ca-conditional-options" style="<?php echo ($ca_check == 'y') ? 'display: block;' : 'display: none;'; ?> margin-left: 20px; margin-top: 10px;">
                    <p><input id="flyer-ca-required" type="checkbox" name="flyer_ca_required" value="y" <?php if ($flyer_ca_required == 'y') echo 'checked'; ?>><label style="display: inline;" for="flyer-ca-required">Property Flyer requires confidentiality agreement signature before download</label></p>
                    <p><input id="om-ca-required" type="checkbox" name="om_ca_required" value="y" <?php if ($om_ca_required == 'y') echo 'checked'; ?>><label style="display: inline;" for="om-ca-required">Property OM (Offering Memorandum) requires confidentiality agreement signature before download</label></p>
                </div>

<div id="ca-content" class="bts-hide">
    <h4>Please enter your confidentiality agreement here</h4>
    <?php
    $ca_settings = array(
        'editor_height' => 425, // In pixels, takes precedence and has no default value
        'textarea_rows' => 20,  // Has no visible effect if editor_height is set, default is 20
    );

    //This function adds the WYSIWYG Editor
    wp_editor(
        $confidential_agreement,
        'confidential_agreement',
        $ca_settings
    );
    ?>
</div>
            </td>
        </tr>
    </tbody>
</table>

<script>
    jQuery(document).ready(function($) {
        // Handle conditional visibility of Flyer and OM checkboxes
        $('#ca-check').on('change', function() {
            if(this.checked) {
                $('#ca-conditional-options').show();
                $('#offer-memorandum-check').prop('checked', true);
            } else {
                $('#ca-conditional-options').hide();
                // Uncheck the conditional options when main checkbox is unchecked
                $('#flyer-ca-required, #om-ca-required').prop('checked', false);
            }
        });

        $('#is-global-ca-check').on('change', function() {
            if(this.checked){
                $('#offer-memorandum-check').prop('checked', true);
            }
        });
    });
</script>
