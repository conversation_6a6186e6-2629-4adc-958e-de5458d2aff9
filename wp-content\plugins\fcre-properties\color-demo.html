<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FCRE Properties Color Demo</title>
    <style>
        /* Demo styles to show how the color system works */
        :root {
            --fcre-primary-color: #007cba;
            --fcre-secondary-color: #005a87;
            --fcre-button-color: #0073aa;
            --fcre-button-hover-color: #005a87;
            --fcre-button-text-color: #ffffff;
            --fcre-button-hover-text-color: #ffffff;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            background-color: var(--fcre-primary-color);
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
        }
        
        .fcre-btn-primary {
            background-color: var(--fcre-button-color);
            color: var(--fcre-button-text-color);
            border: 1px solid var(--fcre-button-color);
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .fcre-btn-primary:hover {
            background-color: var(--fcre-button-hover-color);
            border-color: var(--fcre-button-hover-color);
            color: var(--fcre-button-hover-text-color);
        }
        
        .fcre-property-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            transition: border-color 0.3s ease;
        }
        
        .fcre-property-card:hover {
            border-color: var(--fcre-secondary-color);
        }
        
        .fcre-property-link {
            color: var(--fcre-primary-color);
            text-decoration: none;
            font-weight: bold;
        }
        
        .fcre-property-link:hover {
            color: var(--fcre-button-hover-color);
        }
        
        .fcre-form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            transition: border-color 0.3s ease;
        }
        
        .fcre-form-control:focus {
            outline: none;
            border-color: var(--fcre-primary-color);
            box-shadow: 0 0 0 0.25rem rgba(0, 124, 186, 0.25);
        }
        
        .fcre-tab-titles {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            border-bottom: 1px solid #ddd;
        }
        
        .fcre-tab-titles li {
            padding: 10px 20px;
            background: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            cursor: pointer;
            margin-right: 5px;
        }
        
        .fcre-tab-titles li.active {
            background-color: var(--fcre-primary-color);
            border-color: var(--fcre-primary-color);
            color: white;
        }
        
        .fcre-status-badge {
            background-color: var(--fcre-secondary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .color-info {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .color-swatch {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 4px;
            margin-right: 10px;
            vertical-align: middle;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>FCRE Properties Color System Demo</h1>
            <p>This demonstrates how the color settings from the admin panel affect the frontend styling.</p>
        </div>
        
        <div class="color-info">
            <h3>Current Color Scheme</h3>
            <p>
                <span class="color-swatch" style="background-color: var(--fcre-primary-color);"></span>
                <strong>Primary Color:</strong> Used for headers, main elements, and focus states
            </p>
            <p>
                <span class="color-swatch" style="background-color: var(--fcre-secondary-color);"></span>
                <strong>Secondary Color:</strong> Used for accents, hover states, and badges
            </p>
            <p>
                <span class="color-swatch" style="background-color: var(--fcre-button-color);"></span>
                <strong>Button Color:</strong> Default button background
            </p>
            <p>
                <span class="color-swatch" style="background-color: var(--fcre-button-hover-color);"></span>
                <strong>Button Hover Color:</strong> Button color on hover
            </p>
            <p>
                <span class="color-swatch" style="background-color: var(--fcre-button-text-color);"></span>
                <strong>Button Text Color:</strong> Text color for buttons
            </p>
            <p>
                <span class="color-swatch" style="background-color: var(--fcre-button-hover-text-color);"></span>
                <strong>Button Hover Text Color:</strong> Text color when hovering over buttons
            </p>
        </div>
        
        <div class="demo-section">
            <h3>Buttons</h3>
            <a href="#" class="fcre-btn-primary">Primary Button</a>
            <a href="#" class="fcre-btn-primary">Another Button</a>
            <p><em>Hover over the buttons to see the hover color effect.</em></p>
        </div>
        
        <div class="demo-section">
            <h3>Property Cards</h3>
            <div class="fcre-property-card">
                <h4><a href="#" class="fcre-property-link">Sample Property Title</a></h4>
                <p>123 Main Street, Downtown</p>
                <span class="fcre-status-badge">For Sale</span>
                <p><em>Hover over this card to see the border color change.</em></p>
            </div>
            <div class="fcre-property-card">
                <h4><a href="#" class="fcre-property-link">Another Property</a></h4>
                <p>456 Oak Avenue, Uptown</p>
                <span class="fcre-status-badge">For Lease</span>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>Form Elements</h3>
            <input type="text" class="fcre-form-control" placeholder="Search properties..." />
            <input type="text" class="fcre-form-control" placeholder="Location" />
            <p><em>Click in the input fields to see the focus color effect.</em></p>
        </div>
        
        <div class="demo-section">
            <h3>Tabs</h3>
            <ul class="fcre-tab-titles">
                <li class="active">Overview</li>
                <li>Details</li>
                <li>Photos</li>
                <li>Documents</li>
            </ul>
            <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
                <p>Tab content goes here. The active tab uses the primary color.</p>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>How to Use</h3>
            <ol>
                <li>Go to <strong>SnapCRE → Settings → Colors</strong> in your WordPress admin</li>
                <li>Choose your desired colors using the color pickers</li>
                <li>Save the settings</li>
                <li>The colors will automatically be applied to all frontend elements</li>
            </ol>
            <p><strong>Note:</strong> This demo shows the default colors. Your actual colors will be applied when you save them in the admin panel.</p>
        </div>
    </div>
    
    <script>
        // Simple tab functionality for demo
        document.querySelectorAll('.fcre-tab-titles li').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.fcre-tab-titles li').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
