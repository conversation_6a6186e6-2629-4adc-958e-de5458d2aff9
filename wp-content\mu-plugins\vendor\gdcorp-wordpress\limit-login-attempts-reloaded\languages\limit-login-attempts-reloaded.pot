# Copyright (C) 2024 Limit Login Attempts Reloaded
# This file is distributed under the same license as the Limit Login Attempts Reloaded plugin.
msgid ""
msgstr ""
"Project-Id-Version: Limit Login Attempts Reloaded 2.26.13\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/limit-login-attempts-reloaded\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-08-28T20:03:19+03:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: limit-login-attempts-reloaded\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: core/LimitLoginAttempts.php:169
msgid "Limit Login Attempts Reloaded"
msgstr ""

#. Description of the plugin
msgid "Block excessive login attempts and protect your site against brute force attacks. Simple, yet powerful tools to improve site performance."
msgstr ""

#. Author URI of the plugin
msgid "https://www.limitloginattempts.com/"
msgstr ""

#: core/Ajax.php:175
msgid "Please specify the Setup Code"
msgstr ""

#: core/Ajax.php:359
#: core/Ajax.php:662
msgid "No events yet."
msgstr ""

#: core/Ajax.php:451
msgid "Today at "
msgstr ""

#: core/Ajax.php:538
msgid "Login details could not be populated due to insufficient cloud resources.<br>Please <a class=\"link__style_unlink llar_turquoise\" href=\"%s\" target=\"_blank\">upgrade to Premium</a> to access this data."
msgstr ""

#: core/Ajax.php:549
msgid "Continent: "
msgstr ""

#: core/Ajax.php:558
msgid "Country: "
msgstr ""

#: core/Ajax.php:564
msgid "State/Province: "
msgstr ""

#: core/Ajax.php:570
msgid "District: "
msgstr ""

#: core/Ajax.php:576
msgid "City: "
msgstr ""

#: core/Ajax.php:582
msgid "Zipcode: "
msgstr ""

#: core/Ajax.php:588
msgid "Latitude, Longitude: "
msgstr ""

#: core/Ajax.php:607
msgid "Timezone: "
msgstr ""

#: core/Ajax.php:636
msgid "Internet Provider: "
msgstr ""

#: core/Ajax.php:643
msgid "Connection Type: "
msgstr ""

#: core/Ajax.php:711
msgid "No lockouts yet."
msgstr ""

#: core/Ajax.php:728
msgid "Meanwhile, the app falls over to the <a href=\"%s\">default functionality</a>."
msgstr ""

#: core/Ajax.php:779
msgid "No rules yet."
msgstr ""

#: core/Ajax.php:991
msgid "<strong>%d</strong> attempt remaining."
msgid_plural "<strong>%d</strong> attempts remaining."
msgstr[0] ""
msgstr[1] ""

#: core/Ajax.php:1127
msgid "Wrong email format."
msgstr ""

#: core/Ajax.php:1133
msgid "LLAR Security Notifications [TEST]"
msgstr ""

#: core/Ajax.php:1134
msgid "Your email notifications for Limit Login Attempts Reloaded are working correctly. If this email is going to spam, please be sure to add this address to your safelist."
msgstr ""

#: core/CloudApp.php:132
msgid "The endpoint is not responding. Please contact your app provider to settle that."
msgstr ""

#: core/CloudApp.php:157
msgid "The app has been successfully imported."
msgstr ""

#: core/Config.php:96
msgid "By proceeding you understand and give your consent that your IP address and browser information might be processed by the security plugins installed on this site."
msgstr ""

#: core/LimitLoginAttempts.php:343
#: core/LimitLoginAttempts.php:712
#: views/options-page.php:129
msgid "Dashboard"
msgstr ""

#: core/LimitLoginAttempts.php:344
#: core/LimitLoginAttempts.php:717
#: views/options-page.php:133
msgid "Settings"
msgstr ""

#: core/LimitLoginAttempts.php:354
#: core/LimitLoginAttempts.php:746
msgid "Free Upgrade"
msgstr ""

#: core/LimitLoginAttempts.php:361
#: views/tab-dashboard.php:97
#: views/tab-dashboard.php:253
msgid "Upgrade to Premium"
msgstr ""

#: core/LimitLoginAttempts.php:502
#: core/LimitLoginAttempts.php:1023
#: core/LimitLoginAttempts.php:1491
msgid "<strong>ERROR</strong>: Too many failed login attempts."
msgstr ""

#: core/LimitLoginAttempts.php:509
#: core/LimitLoginAttempts.php:1030
#: core/LimitLoginAttempts.php:1508
msgid "Please try again in %d hour."
msgid_plural "Please try again in %d hours."
msgstr[0] ""
msgstr[1] ""

#: core/LimitLoginAttempts.php:511
#: core/LimitLoginAttempts.php:1032
#: core/LimitLoginAttempts.php:1511
msgid "Please try again in %d minute."
msgid_plural "Please try again in %d minutes."
msgstr[0] ""
msgstr[1] ""

#: core/LimitLoginAttempts.php:723
#: resources/compare-plans-data.php:64
#: views/options-page.php:139
msgid "Login Firewall"
msgstr ""

#: core/LimitLoginAttempts.php:728
#: views/options-page.php:145
msgid "Logs"
msgstr ""

#: core/LimitLoginAttempts.php:733
#: views/options-page.php:151
msgid "Debug"
msgstr ""

#: core/LimitLoginAttempts.php:738
#: views/options-page.php:155
#: views/tab-dashboard.php:128
msgid "Help"
msgstr ""

#: core/LimitLoginAttempts.php:746
msgid "Premium"
msgstr ""

#: core/LimitLoginAttempts.php:829
msgid "LLAR"
msgstr ""

#: core/LimitLoginAttempts.php:1216
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#: core/LimitLoginAttempts.php:1223
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

#: core/LimitLoginAttempts.php:1260
msgid "Failed login by IP %s www.limitloginattempts.com"
msgstr ""

#: core/LimitLoginAttempts.php:1499
msgid "Please try again later."
msgstr ""

#: core/LimitLoginAttempts.php:1545
msgid "<strong>ERROR</strong>: Incorrect email address or password."
msgstr ""

#: core/LimitLoginAttempts.php:1548
msgid "<strong>ERROR</strong>: Incorrect username or password."
msgstr ""

#: core/LimitLoginAttempts.php:1564
msgid "WC Error"
msgstr ""

#: core/LimitLoginAttempts.php:1751
msgid "Cleared IP log"
msgstr ""

#: core/LimitLoginAttempts.php:1758
msgid "Reset lockout count"
msgstr ""

#: core/LimitLoginAttempts.php:1765
msgid "Cleared current lockouts"
msgstr ""

#: core/LimitLoginAttempts.php:1810
msgid "The %s IP range is invalid"
msgstr ""

#: core/LimitLoginAttempts.php:1837
#: core/LimitLoginAttempts.php:1910
msgid "Settings saved."
msgstr ""

#: core/LimitLoginAttempts.php:2063
msgid "Hey <strong>Limit Login Attempts Reloaded</strong> user!"
msgstr ""

#: core/LimitLoginAttempts.php:2064
msgid "A <strong>crazy idea</strong> we wanted to share! What if we put an image from YOU on the <a href=\"https://wordpress.org/plugins/limit-login-attempts-reloaded/\" target=\"_blank\">LLAR page</a>?! (<a href=\"https://wordpress.org/plugins/hello-dolly/\" target=\"_blank\">example</a>) A drawing made by you or your child would cheer people up! Send us your drawing by <a href=\"mailto:<EMAIL>\" target=\"_blank\">email</a> and we like it, we'll add it in the next release. Let's have some fun!"
msgstr ""

#: core/LimitLoginAttempts.php:2065
msgid "We would really like to hear your feedback about the plugin! Please take a couple minutes to write a few words <a href=\"https://wordpress.org/support/plugin/limit-login-attempts-reloaded/reviews/#new-post\" target=\"_blank\">here</a>. Thank you!"
msgstr ""

#: core/LimitLoginAttempts.php:2068
msgid "Don't show again"
msgstr ""

#: core/LimitLoginAttempts.php:2069
msgid "Maybe later"
msgstr ""

#: core/LimitLoginAttempts.php:2070
msgid "Leave a review"
msgstr ""

#: core/LimitLoginAttempts.php:2156
msgid "You have been upgraded to the latest version of <strong>Limit Login Attempts Reloaded</strong>.<br> Due to increased security threats around the holidays, we recommend turning on email notifications when you receive a failed login attempt."
msgstr ""

#: core/LimitLoginAttempts.php:2161
msgid "Yes, turn on email notifications"
msgstr ""

#: core/LimitLoginAttempts.php:2162
msgid "Remind me a month from now"
msgstr ""

#: core/LimitLoginAttempts.php:2163
msgid "Don't show this message again"
msgstr ""

#: resources/compare-plans-data.php:27
msgid "Installed"
msgstr ""

#: resources/compare-plans-data.php:31
msgid "Get Started (Free)"
msgstr ""

#: resources/compare-plans-data.php:35
msgid "Upgrade now"
msgstr ""

#: resources/compare-plans-data.php:50
msgid "Limit Number of Retry Attempts"
msgstr ""

#: resources/compare-plans-data.php:57
msgid "Configurable Lockout Timing"
msgstr ""

#: resources/compare-plans-data.php:65
msgid "Secure your login page with our cutting-edge login firewall, defending against unauthorized access attempts and protecting your users' accounts and sensitive information."
msgstr ""

#: resources/compare-plans-data.php:72
msgid "Performance Optimizer"
msgstr ""

#: resources/compare-plans-data.php:73
msgid "Absorb failed login attempts from brute force bots in the cloud to keep your website at its optimal performance."
msgstr ""

#: resources/compare-plans-data.php:75
msgid "1k for first month%s(100 per month after)"
msgstr ""

#: resources/compare-plans-data.php:76
msgid "100k requests per month"
msgstr ""

#: resources/compare-plans-data.php:77
msgid "200k requests per month"
msgstr ""

#: resources/compare-plans-data.php:78
msgid "300k requests per month"
msgstr ""

#: resources/compare-plans-data.php:80
msgid "Successful Login Logs"
msgstr ""

#: resources/compare-plans-data.php:81
msgid "Ensure the security and integrity of your website by logging your successful logins."
msgstr ""

#: resources/compare-plans-data.php:88
msgid "Block By Country"
msgstr ""

#: resources/compare-plans-data.php:89
msgid "Disable IPs from any region to disable logins."
msgstr ""

#: resources/compare-plans-data.php:96
msgid "Access Blocklist of Malicious IPs"
msgstr ""

#: resources/compare-plans-data.php:97
msgid "Add another layer of protection from brute force bots by accessing a global database of known IPs with malicious activity."
msgstr ""

#: resources/compare-plans-data.php:104
msgid "Auto IP Blocklist"
msgstr ""

#: resources/compare-plans-data.php:105
msgid "Automatically add malicious IPs to your blocklist when triggered by the system."
msgstr ""

#: resources/compare-plans-data.php:112
msgid "Access Active Cloud Blocklist"
msgstr ""

#: resources/compare-plans-data.php:113
msgid "Use system wide data from over 10,000 WordPress websites to identify and block malicious IPs. This is an active list in real-time."
msgstr ""

#: resources/compare-plans-data.php:120
msgid "Intelligent IP Blocking"
msgstr ""

#: resources/compare-plans-data.php:121
msgid "Use active IP database via the cloud to automatically block users before they are able to make a failed login."
msgstr ""

#: resources/compare-plans-data.php:128
msgid "Synchronize Lockouts & Safelists/Blocklists"
msgstr ""

#: resources/compare-plans-data.php:129
msgid "Lockouts & safelists/blocklists can be shared between multiple domains to enhance protection."
msgstr ""

#: resources/compare-plans-data.php:136
#: views/tab-help.php:173
msgid "Premium Support"
msgstr ""

#: resources/compare-plans-data.php:138
msgid "Receive 1 on 1 technical support via email for any issues. Free support availabe in the <a href=\"%s\" target=\"_blank\">WordPress support forum</a>."
msgstr ""

#: resources/continent.php:3
msgid "Asia"
msgstr ""

#: resources/continent.php:4
#: resources/countries.php:249
msgid "Antarctica"
msgstr ""

#: resources/continent.php:5
msgid "Africa"
msgstr ""

#: resources/continent.php:6
msgid "Europe"
msgstr ""

#: resources/continent.php:7
msgid "North America"
msgstr ""

#: resources/continent.php:8
msgid "Oceania"
msgstr ""

#: resources/continent.php:9
msgid "South America"
msgstr ""

#: resources/continent.php:10
msgid "Intercontinental"
msgstr ""

#: resources/continent.php:11
#: resources/countries.php:3
msgid "Unknown"
msgstr ""

#: resources/countries.php:4
msgid "Rwanda"
msgstr ""

#: resources/countries.php:5
msgid "Somalia"
msgstr ""

#: resources/countries.php:6
msgid "Yemen"
msgstr ""

#: resources/countries.php:7
msgid "Iraq"
msgstr ""

#: resources/countries.php:8
msgid "Saudi Arabia"
msgstr ""

#: resources/countries.php:9
msgid "Iran"
msgstr ""

#: resources/countries.php:10
msgid "Cyprus"
msgstr ""

#: resources/countries.php:11
msgid "Tanzania"
msgstr ""

#: resources/countries.php:12
msgid "Syria"
msgstr ""

#: resources/countries.php:13
msgid "Armenia"
msgstr ""

#: resources/countries.php:14
msgid "Kenya"
msgstr ""

#: resources/countries.php:15
msgid "DR Congo"
msgstr ""

#: resources/countries.php:16
msgid "Djibouti"
msgstr ""

#: resources/countries.php:17
msgid "Uganda"
msgstr ""

#: resources/countries.php:18
msgid "Central African Republic"
msgstr ""

#: resources/countries.php:19
msgid "Seychelles"
msgstr ""

#: resources/countries.php:20
msgid "Jordan"
msgstr ""

#: resources/countries.php:21
msgid "Lebanon"
msgstr ""

#: resources/countries.php:22
msgid "Kuwait"
msgstr ""

#: resources/countries.php:23
msgid "Oman"
msgstr ""

#: resources/countries.php:24
msgid "Qatar"
msgstr ""

#: resources/countries.php:25
msgid "Bahrain"
msgstr ""

#: resources/countries.php:26
msgid "United Arab Emirates"
msgstr ""

#: resources/countries.php:27
msgid "Israel"
msgstr ""

#: resources/countries.php:28
msgid "Turkey"
msgstr ""

#: resources/countries.php:29
msgid "Ethiopia"
msgstr ""

#: resources/countries.php:30
msgid "Eritrea"
msgstr ""

#: resources/countries.php:31
msgid "Egypt"
msgstr ""

#: resources/countries.php:32
msgid "Sudan"
msgstr ""

#: resources/countries.php:33
msgid "Greece"
msgstr ""

#: resources/countries.php:34
msgid "Burundi"
msgstr ""

#: resources/countries.php:35
msgid "Estonia"
msgstr ""

#: resources/countries.php:36
msgid "Latvia"
msgstr ""

#: resources/countries.php:37
msgid "Azerbaijan"
msgstr ""

#: resources/countries.php:38
msgid "Lithuania"
msgstr ""

#: resources/countries.php:39
msgid "Svalbard and Jan Mayen"
msgstr ""

#: resources/countries.php:40
msgid "Georgia"
msgstr ""

#: resources/countries.php:41
msgid "Moldova"
msgstr ""

#: resources/countries.php:42
msgid "Belarus"
msgstr ""

#: resources/countries.php:43
msgid "Finland"
msgstr ""

#: resources/countries.php:44
msgid "Åland"
msgstr ""

#: resources/countries.php:45
msgid "Ukraine"
msgstr ""

#: resources/countries.php:46
msgid "North Macedonia"
msgstr ""

#: resources/countries.php:47
msgid "Hungary"
msgstr ""

#: resources/countries.php:48
msgid "Bulgaria"
msgstr ""

#: resources/countries.php:49
msgid "Albania"
msgstr ""

#: resources/countries.php:50
msgid "Poland"
msgstr ""

#: resources/countries.php:51
msgid "Romania"
msgstr ""

#: resources/countries.php:52
msgid "Kosovo"
msgstr ""

#: resources/countries.php:53
msgid "Zimbabwe"
msgstr ""

#: resources/countries.php:54
msgid "Zambia"
msgstr ""

#: resources/countries.php:55
msgid "Comoros"
msgstr ""

#: resources/countries.php:56
msgid "Malawi"
msgstr ""

#: resources/countries.php:57
msgid "Lesotho"
msgstr ""

#: resources/countries.php:58
msgid "Botswana"
msgstr ""

#: resources/countries.php:59
msgid "Mauritius"
msgstr ""

#: resources/countries.php:60
msgid "Eswatini"
msgstr ""

#: resources/countries.php:61
msgid "Réunion"
msgstr ""

#: resources/countries.php:62
msgid "South Africa"
msgstr ""

#: resources/countries.php:63
msgid "Mayotte"
msgstr ""

#: resources/countries.php:64
msgid "Mozambique"
msgstr ""

#: resources/countries.php:65
msgid "Madagascar"
msgstr ""

#: resources/countries.php:66
msgid "Afghanistan"
msgstr ""

#: resources/countries.php:67
msgid "Pakistan"
msgstr ""

#: resources/countries.php:68
msgid "Bangladesh"
msgstr ""

#: resources/countries.php:69
msgid "Turkmenistan"
msgstr ""

#: resources/countries.php:70
msgid "Tajikistan"
msgstr ""

#: resources/countries.php:71
msgid "Sri Lanka"
msgstr ""

#: resources/countries.php:72
msgid "Bhutan"
msgstr ""

#: resources/countries.php:73
msgid "India"
msgstr ""

#: resources/countries.php:74
msgid "Maldives"
msgstr ""

#: resources/countries.php:75
msgid "British Indian Ocean Territory"
msgstr ""

#: resources/countries.php:76
msgid "Nepal"
msgstr ""

#: resources/countries.php:77
msgid "Myanmar"
msgstr ""

#: resources/countries.php:78
msgid "Uzbekistan"
msgstr ""

#: resources/countries.php:79
msgid "Kazakhstan"
msgstr ""

#: resources/countries.php:80
msgid "Kyrgyzstan"
msgstr ""

#: resources/countries.php:81
msgid "French Southern Territories"
msgstr ""

#: resources/countries.php:82
msgid "Heard Island and McDonald Islands"
msgstr ""

#: resources/countries.php:83
msgid "Cocos [Keeling] Islands"
msgstr ""

#: resources/countries.php:84
msgid "Palau"
msgstr ""

#: resources/countries.php:85
msgid "Vietnam"
msgstr ""

#: resources/countries.php:86
msgid "Thailand"
msgstr ""

#: resources/countries.php:87
msgid "Indonesia"
msgstr ""

#: resources/countries.php:88
msgid "Laos"
msgstr ""

#: resources/countries.php:89
msgid "Taiwan"
msgstr ""

#: resources/countries.php:90
msgid "Philippines"
msgstr ""

#: resources/countries.php:91
msgid "Malaysia"
msgstr ""

#: resources/countries.php:92
msgid "China"
msgstr ""

#: resources/countries.php:93
msgid "Hong Kong"
msgstr ""

#: resources/countries.php:94
msgid "Brunei"
msgstr ""

#: resources/countries.php:95
msgid "Macao"
msgstr ""

#: resources/countries.php:96
msgid "Cambodia"
msgstr ""

#: resources/countries.php:97
msgid "South Korea"
msgstr ""

#: resources/countries.php:98
msgid "Japan"
msgstr ""

#: resources/countries.php:99
msgid "North Korea"
msgstr ""

#: resources/countries.php:100
msgid "Singapore"
msgstr ""

#: resources/countries.php:101
msgid "Cook Islands"
msgstr ""

#: resources/countries.php:102
msgid "East Timor"
msgstr ""

#: resources/countries.php:103
msgid "Russia"
msgstr ""

#: resources/countries.php:104
msgid "Mongolia"
msgstr ""

#: resources/countries.php:105
msgid "Australia"
msgstr ""

#: resources/countries.php:106
msgid "Christmas Island"
msgstr ""

#: resources/countries.php:107
msgid "Marshall Islands"
msgstr ""

#: resources/countries.php:108
msgid "Federated States of Micronesia"
msgstr ""

#: resources/countries.php:109
msgid "Papua New Guinea"
msgstr ""

#: resources/countries.php:110
msgid "Solomon Islands"
msgstr ""

#: resources/countries.php:111
msgid "Tuvalu"
msgstr ""

#: resources/countries.php:112
msgid "Nauru"
msgstr ""

#: resources/countries.php:113
msgid "Vanuatu"
msgstr ""

#: resources/countries.php:114
msgid "New Caledonia"
msgstr ""

#: resources/countries.php:115
msgid "Norfolk Island"
msgstr ""

#: resources/countries.php:116
msgid "New Zealand"
msgstr ""

#: resources/countries.php:117
msgid "Fiji"
msgstr ""

#: resources/countries.php:118
msgid "Libya"
msgstr ""

#: resources/countries.php:119
msgid "Cameroon"
msgstr ""

#: resources/countries.php:120
msgid "Senegal"
msgstr ""

#: resources/countries.php:121
msgid "Congo Republic"
msgstr ""

#: resources/countries.php:122
msgid "Portugal"
msgstr ""

#: resources/countries.php:123
msgid "Liberia"
msgstr ""

#: resources/countries.php:124
msgid "Ivory Coast"
msgstr ""

#: resources/countries.php:125
msgid "Ghana"
msgstr ""

#: resources/countries.php:126
msgid "Equatorial Guinea"
msgstr ""

#: resources/countries.php:127
msgid "Nigeria"
msgstr ""

#: resources/countries.php:128
msgid "Burkina Faso"
msgstr ""

#: resources/countries.php:129
msgid "Togo"
msgstr ""

#: resources/countries.php:130
msgid "Guinea-Bissau"
msgstr ""

#: resources/countries.php:131
msgid "Mauritania"
msgstr ""

#: resources/countries.php:132
msgid "Benin"
msgstr ""

#: resources/countries.php:133
msgid "Gabon"
msgstr ""

#: resources/countries.php:134
msgid "Sierra Leone"
msgstr ""

#: resources/countries.php:135
msgid "São Tomé and Príncipe"
msgstr ""

#: resources/countries.php:136
msgid "Gibraltar"
msgstr ""

#: resources/countries.php:137
msgid "Gambia"
msgstr ""

#: resources/countries.php:138
msgid "Guinea"
msgstr ""

#: resources/countries.php:139
msgid "Chad"
msgstr ""

#: resources/countries.php:140
msgid "Niger"
msgstr ""

#: resources/countries.php:141
msgid "Mali"
msgstr ""

#: resources/countries.php:142
msgid "Western Sahara"
msgstr ""

#: resources/countries.php:143
msgid "Tunisia"
msgstr ""

#: resources/countries.php:144
msgid "Spain"
msgstr ""

#: resources/countries.php:145
msgid "Morocco"
msgstr ""

#: resources/countries.php:146
msgid "Malta"
msgstr ""

#: resources/countries.php:147
msgid "Algeria"
msgstr ""

#: resources/countries.php:148
msgid "Faroe Islands"
msgstr ""

#: resources/countries.php:149
msgid "Denmark"
msgstr ""

#: resources/countries.php:150
msgid "Iceland"
msgstr ""

#: resources/countries.php:151
msgid "United Kingdom"
msgstr ""

#: resources/countries.php:152
msgid "Switzerland"
msgstr ""

#: resources/countries.php:153
msgid "Sweden"
msgstr ""

#: resources/countries.php:154
msgid "Netherlands"
msgstr ""

#: resources/countries.php:155
msgid "Austria"
msgstr ""

#: resources/countries.php:156
msgid "Belgium"
msgstr ""

#: resources/countries.php:157
msgid "Germany"
msgstr ""

#: resources/countries.php:158
msgid "Luxembourg"
msgstr ""

#: resources/countries.php:159
msgid "Ireland"
msgstr ""

#: resources/countries.php:160
msgid "Monaco"
msgstr ""

#: resources/countries.php:161
msgid "France"
msgstr ""

#: resources/countries.php:162
msgid "Andorra"
msgstr ""

#: resources/countries.php:163
msgid "Liechtenstein"
msgstr ""

#: resources/countries.php:164
msgid "Jersey"
msgstr ""

#: resources/countries.php:165
msgid "Isle of Man"
msgstr ""

#: resources/countries.php:166
msgid "Guernsey"
msgstr ""

#: resources/countries.php:167
msgid "Slovakia"
msgstr ""

#: resources/countries.php:168
msgid "Czechia"
msgstr ""

#: resources/countries.php:169
msgid "Norway"
msgstr ""

#: resources/countries.php:170
msgid "Vatican City"
msgstr ""

#: resources/countries.php:171
msgid "San Marino"
msgstr ""

#: resources/countries.php:172
msgid "Italy"
msgstr ""

#: resources/countries.php:173
msgid "Slovenia"
msgstr ""

#: resources/countries.php:174
msgid "Montenegro"
msgstr ""

#: resources/countries.php:175
msgid "Croatia"
msgstr ""

#: resources/countries.php:176
msgid "Bosnia and Herzegovina"
msgstr ""

#: resources/countries.php:177
msgid "Angola"
msgstr ""

#: resources/countries.php:178
msgid "Namibia"
msgstr ""

#: resources/countries.php:179
msgid "Saint Helena"
msgstr ""

#: resources/countries.php:180
msgid "Bouvet Island"
msgstr ""

#: resources/countries.php:181
msgid "Barbados"
msgstr ""

#: resources/countries.php:182
msgid "Cabo Verde"
msgstr ""

#: resources/countries.php:183
msgid "Guyana"
msgstr ""

#: resources/countries.php:184
msgid "French Guiana"
msgstr ""

#: resources/countries.php:185
msgid "Suriname"
msgstr ""

#: resources/countries.php:186
msgid "Saint Pierre and Miquelon"
msgstr ""

#: resources/countries.php:187
msgid "Greenland"
msgstr ""

#: resources/countries.php:188
msgid "Paraguay"
msgstr ""

#: resources/countries.php:189
msgid "Uruguay"
msgstr ""

#: resources/countries.php:190
msgid "Brazil"
msgstr ""

#: resources/countries.php:191
msgid "Falkland Islands"
msgstr ""

#: resources/countries.php:192
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: resources/countries.php:193
msgid "Jamaica"
msgstr ""

#: resources/countries.php:194
msgid "Dominican Republic"
msgstr ""

#: resources/countries.php:195
msgid "Cuba"
msgstr ""

#: resources/countries.php:196
msgid "Martinique"
msgstr ""

#: resources/countries.php:197
msgid "Bahamas"
msgstr ""

#: resources/countries.php:198
msgid "Bermuda"
msgstr ""

#: resources/countries.php:199
msgid "Anguilla"
msgstr ""

#: resources/countries.php:200
msgid "Trinidad and Tobago"
msgstr ""

#: resources/countries.php:201
msgid "St Kitts and Nevis"
msgstr ""

#: resources/countries.php:202
msgid "Dominica"
msgstr ""

#: resources/countries.php:203
msgid "Antigua and Barbuda"
msgstr ""

#: resources/countries.php:204
msgid "Saint Lucia"
msgstr ""

#: resources/countries.php:205
msgid "Turks and Caicos Islands"
msgstr ""

#: resources/countries.php:206
msgid "Aruba"
msgstr ""

#: resources/countries.php:207
msgid "British Virgin Islands"
msgstr ""

#: resources/countries.php:208
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: resources/countries.php:209
msgid "Montserrat"
msgstr ""

#: resources/countries.php:210
msgid "Saint Martin"
msgstr ""

#: resources/countries.php:211
msgid "Saint Barthélemy"
msgstr ""

#: resources/countries.php:212
msgid "Guadeloupe"
msgstr ""

#: resources/countries.php:213
msgid "Grenada"
msgstr ""

#: resources/countries.php:214
msgid "Cayman Islands"
msgstr ""

#: resources/countries.php:215
msgid "Belize"
msgstr ""

#: resources/countries.php:216
msgid "El Salvador"
msgstr ""

#: resources/countries.php:217
msgid "Guatemala"
msgstr ""

#: resources/countries.php:218
msgid "Honduras"
msgstr ""

#: resources/countries.php:219
msgid "Nicaragua"
msgstr ""

#: resources/countries.php:220
msgid "Costa Rica"
msgstr ""

#: resources/countries.php:221
msgid "Venezuela"
msgstr ""

#: resources/countries.php:222
msgid "Ecuador"
msgstr ""

#: resources/countries.php:223
msgid "Colombia"
msgstr ""

#: resources/countries.php:224
msgid "Panama"
msgstr ""

#: resources/countries.php:225
msgid "Haiti"
msgstr ""

#: resources/countries.php:226
msgid "Argentina"
msgstr ""

#: resources/countries.php:227
msgid "Chile"
msgstr ""

#: resources/countries.php:228
msgid "Bolivia"
msgstr ""

#: resources/countries.php:229
msgid "Peru"
msgstr ""

#: resources/countries.php:230
msgid "Mexico"
msgstr ""

#: resources/countries.php:231
msgid "French Polynesia"
msgstr ""

#: resources/countries.php:232
msgid "Pitcairn Islands"
msgstr ""

#: resources/countries.php:233
msgid "Kiribati"
msgstr ""

#: resources/countries.php:234
msgid "Tokelau"
msgstr ""

#: resources/countries.php:235
msgid "Tonga"
msgstr ""

#: resources/countries.php:236
msgid "Wallis and Futuna"
msgstr ""

#: resources/countries.php:237
msgid "Samoa"
msgstr ""

#: resources/countries.php:238
msgid "Niue"
msgstr ""

#: resources/countries.php:239
msgid "Northern Mariana Islands"
msgstr ""

#: resources/countries.php:240
msgid "Guam"
msgstr ""

#: resources/countries.php:241
msgid "Puerto Rico"
msgstr ""

#: resources/countries.php:242
msgid "U.S. Virgin Islands"
msgstr ""

#: resources/countries.php:243
msgid "U.S. Minor Outlying Islands"
msgstr ""

#: resources/countries.php:244
msgid "American Samoa"
msgstr ""

#: resources/countries.php:245
msgid "Canada"
msgstr ""

#: resources/countries.php:246
msgid "United States"
msgstr ""

#: resources/countries.php:247
msgid "Palestine"
msgstr ""

#: resources/countries.php:248
msgid "Serbia"
msgstr ""

#: resources/countries.php:250
msgid "Sint Maarten"
msgstr ""

#: resources/countries.php:251
msgid "Curaçao"
msgstr ""

#: resources/countries.php:252
msgid "Bonaire, Sint Eustatius, and Saba"
msgstr ""

#: resources/countries.php:253
msgid "South Sudan"
msgstr ""

#: views/app-widgets/acl-rules.php:10
msgid "Login Access Rules"
msgstr ""

#: views/app-widgets/acl-rules.php:14
#: views/app-widgets/acl-rules.php:63
msgid "Documentation"
msgstr ""

#: views/app-widgets/acl-rules.php:24
#: views/app-widgets/acl-rules.php:33
#: views/app-widgets/acl-rules.php:73
#: views/app-widgets/acl-rules.php:82
#: views/app-widgets/event-log.php:43
msgid "Pattern"
msgstr ""

#: views/app-widgets/acl-rules.php:25
#: views/app-widgets/acl-rules.php:74
#: views/app-widgets/event-log.php:41
msgid "Rule"
msgstr ""

#: views/app-widgets/acl-rules.php:26
#: views/app-widgets/acl-rules.php:75
msgid "Action"
msgstr ""

#: views/app-widgets/acl-rules.php:37
#: views/app-widgets/acl-rules.php:86
#: views/app-widgets/country-access-rules.php:55
msgid "Deny"
msgstr ""

#: views/app-widgets/acl-rules.php:38
#: views/app-widgets/acl-rules.php:87
msgid "Allow"
msgstr ""

#: views/app-widgets/acl-rules.php:39
#: views/app-widgets/acl-rules.php:88
msgid "Pass"
msgstr ""

#: views/app-widgets/acl-rules.php:44
#: views/app-widgets/acl-rules.php:93
#: views/app-widgets/country-access-rules.php:26
msgid "Add"
msgstr ""

#: views/app-widgets/acl-rules.php:59
msgid "IP Access Rules"
msgstr ""

#: views/app-widgets/active-lockouts.php:8
msgid "Active Lockouts"
msgstr ""

#: views/app-widgets/active-lockouts.php:13
msgid "Reload"
msgstr ""

#: views/app-widgets/active-lockouts.php:22
#: views/app-widgets/event-log.php:38
#: views/app-widgets/login-attempts.php:51
msgid "IP"
msgstr ""

#: views/app-widgets/active-lockouts.php:23
#: views/app-widgets/event-log.php:40
#: views/app-widgets/login-attempts.php:50
msgid "Login"
msgstr ""

#: views/app-widgets/active-lockouts.php:24
msgid "Count"
msgstr ""

#: views/app-widgets/active-lockouts.php:25
msgid "Expires in (minutes)"
msgstr ""

#: views/app-widgets/country-access-rules.php:14
msgid "Country Access Rules"
msgstr ""

#: views/app-widgets/country-access-rules.php:15
msgid "To block all countries but your own add your country as Allow Only rule"
msgstr ""

#: views/app-widgets/country-access-rules.php:22
msgid "these countries:"
msgstr ""

#: views/app-widgets/country-access-rules.php:56
msgid "Allow only"
msgstr ""

#: views/app-widgets/event-log.php:14
msgid "Event Log"
msgstr ""

#: views/app-widgets/event-log.php:19
msgid "Full Logs"
msgstr ""

#: views/app-widgets/event-log.php:24
msgid "All attempts blocked by access rules are hidden by default. You can see the full log at this link."
msgstr ""

#: views/app-widgets/event-log.php:37
#: views/app-widgets/login-attempts.php:49
msgid "Time"
msgstr ""

#: views/app-widgets/event-log.php:39
#: views/tab-logs-local.php:220
msgid "Gateway"
msgstr ""

#: views/app-widgets/event-log.php:42
msgid "Reason"
msgstr ""

#: views/app-widgets/event-log.php:44
msgid "Attempts Left"
msgstr ""

#: views/app-widgets/event-log.php:45
msgid "Lockout Duration"
msgstr ""

#: views/app-widgets/event-log.php:46
msgid "Actions"
msgstr ""

#: views/app-widgets/event-log.php:54
#: views/app-widgets/login-attempts.php:62
msgid "Load older events"
msgstr ""

#: views/app-widgets/event-log.php:61
msgid "Loading older events, skipping ACL events. <a href=\"%s\" target=\"_blank\">Full logs</a>"
msgstr ""

#: views/app-widgets/login-attempts.php:24
#: views/app-widgets/login-attempts.php:38
msgid "Successful Login Attempts"
msgstr ""

#: views/app-widgets/login-attempts.php:28
msgid " View more"
msgstr ""

#: views/app-widgets/login-attempts.php:52
msgid "Role"
msgstr ""

#: views/app-widgets/login-attempts.php:84
msgid "View a complete history of successful logins for your WordPress account"
msgstr ""

#: views/app-widgets/login-attempts.php:87
msgid "All logs are stored in the cloud to ensure malicious users are unable to delete or manipulate site login data."
msgstr ""

#: views/app-widgets/login-attempts.php:91
msgid "This feature is only available for<br><a class=\"link__style_unlink llar_turquoise\" href=\"%s\">Premium</a> users."
msgstr ""

#: views/app-widgets/login-attempts.php:93
msgid "This feature is only available for<br><a class=\"link__style_unlink llar_turquoise\" href=\"%s\">Premium</a> and <a class=\"link__style_unlink llar_turquoise button_micro_cloud\">Micro Cloud (FREE!)</a> users."
msgstr ""

#: views/chart-circle-failed-attempts-today.php:40
#: views/chart-circle-failed-attempts-today.php:75
msgid "Hooray! Zero failed login attempts (past 24 hrs)"
msgstr ""

#: views/chart-circle-failed-attempts-today.php:45
#: views/chart-circle-failed-attempts-today.php:79
msgid "%d failed login attempt "
msgid_plural "%d failed login attempts "
msgstr[0] ""
msgstr[1] ""

#: views/chart-circle-failed-attempts-today.php:46
#: views/chart-circle-failed-attempts-today.php:80
msgid "(past 24 hrs)"
msgstr ""

#: views/chart-circle-failed-attempts-today.php:47
#: views/chart-circle-failed-attempts-today.php:81
msgid "Your site is currently at a low risk for brute force activity"
msgstr ""

#: views/chart-circle-failed-attempts-today.php:51
msgid "Warning: Your site has experienced over 100 failed login attempts in the past 24 hours"
msgstr ""

#: views/chart-circle-failed-attempts-today.php:55
#: views/chart-circle-failed-attempts-today.php:87
msgid "Based on your level of brute force activity, we recommend <a href=\"%s\" class=\"llar_orange\" target=\"_blank\">upgrading to premium</a> to access features to reduce failed logins and improve site performance."
msgstr ""

#: views/chart-circle-failed-attempts-today.php:59
msgid "Based on your level of brute force activity, we recommend <a class=\"llar_orange %s\">free Micro Cloud upgrade</a> to access features to reduce failed logins and improve site performance."
msgstr ""

#: views/chart-circle-failed-attempts-today.php:93
msgid "Failed Login Attempts Today"
msgstr ""

#: views/chart-circle-failed-attempts-today.php:102
#: views/chart-failed-attempts.php:36
#: views/chart-failed-attempts.php:121
#: views/chart-failed-attempts.php:131
msgid "Failed Login Attempts"
msgstr ""

#: views/chart-circle-failed-attempts-today.php:108
#: views/chart-failed-attempts.php:137
msgid "An IP that hasn't been previously denied by the cloud app, but has made an unsuccessful login attempt on your website."
msgstr ""

#: views/chart-circle-failed-attempts-today.php:109
#: views/chart-failed-attempts.php:138
msgid "An IP that has made an unsuccessful login attempt on your website."
msgstr ""

#: views/chart-circle-failed-attempts-today.php:120
msgid "Cloud protection enabled"
msgstr ""

#: views/chart-failed-attempts.php:44
#: views/chart-failed-attempts.php:147
#: views/chart-failed-attempts.php:244
msgid "Requests"
msgstr ""

#: views/chart-failed-attempts.php:152
msgid "A request is utilized when the cloud validates whether an IP address is allowed to attempt a login, which also includes denied logins."
msgstr ""

#: views/chart-failed-attempts.php:162
msgid "Monthly Usage: "
msgstr ""

#: views/chart-failed-attempts.php:227
msgid "Attempts"
msgstr ""

#: views/emails/failed-login.php:243
msgid "Hello {name},"
msgstr ""

#: views/emails/failed-login.php:244
msgid "Hello,"
msgstr ""

#: views/emails/failed-login.php:246
msgid "This notification was sent automatically via Limit Login Attempts Reloaded Plugin."
msgstr ""

#: views/emails/failed-login.php:248
msgid "This is installed on your <b>{domain}</b> WordPress site."
msgstr ""

#: views/emails/failed-login.php:250
msgid "The failed login details include:"
msgstr ""

#: views/emails/failed-login.php:253
msgid "{attempts_count} failed login attempts ({lockouts_count} lockout(s)) from IP <b>{ip_address}</b>"
msgstr ""

#: views/emails/failed-login.php:256
msgid "Last user attempted: <b>{username}</b>"
msgstr ""

#: views/emails/failed-login.php:258
msgid "IP was blocked for {blocked_duration}"
msgstr ""

#: views/emails/failed-login.php:262
msgid "Please visit your WordPress dashboard for additional details, investigation options, and help articles."
msgstr ""

#: views/emails/failed-login.php:276
msgid "Go to Dashboard"
msgstr ""

#: views/emails/failed-login.php:292
msgid "<b>Experiencing frequent attacks or degraded performance?</b> You can now receive premium protection for FREE with "
msgstr ""

#: views/emails/failed-login.php:295
msgid "Micro Cloud©."
msgstr ""

#: views/emails/failed-login.php:296
msgid " Go to your LLAR dashboard to get starte."
msgstr ""

#: views/emails/failed-login.php:305
msgid "Frequently Asked Questions"
msgstr ""

#: views/emails/failed-login.php:307
msgid "What is a failed login attempt?"
msgstr ""

#: views/emails/failed-login.php:308
msgid "A failed login attempt is when an IP address uses incorrect credentials to log into your website. The IP address could be a human operator, or a program designed to guess your password."
msgstr ""

#: views/emails/failed-login.php:310
msgid "Why am I getting these emails?"
msgstr ""

#: views/emails/failed-login.php:311
msgid "You are receiving this email because there was a failed login attempt on your website {domain}. If you'd like to opt out of these notifications, please click the “Unsubscribe” link below."
msgstr ""

#: views/emails/failed-login.php:313
msgid "How dangerous is this failed login attempt?"
msgstr ""

#: views/emails/failed-login.php:314
msgid "Unfortunately, we cannot determine the severity of the IP address with the free version of the plugin. If the IP continues to make attempts and is not recognized by your organization, then it's likely to have malicious intent. Depending on how frequent the attacks are, you may experience performance issues. In the plugin dashboard, you can investigate the frequency of the failed login attempts in the logs and take additional steps to protect your website (i.e. adding them to the block list). You can visit the "
msgstr ""

#: views/emails/failed-login.php:317
msgid "Limit Login Attempts Reloaded website"
msgstr ""

#: views/emails/failed-login.php:317
msgid " for more information on our premium services, which can automatically block and detect malicious IP addresses."
msgstr ""

#: views/emails/failed-login.php:327
msgid "This alert was sent by your website where Limit Login Attempts Reloaded free version is installed and you are listed as the admin. If you are a GoDaddy customer, the plugin is installed into a must-use (MU) folder."
msgstr ""

#: views/emails/failed-login.php:347
msgid "Unsubscribe"
msgstr ""

#: views/emails/failed-login.php:348
msgid "from these notifications."
msgstr ""

#: views/micro-cloud-modal.php:26
msgid "Get Started with Micro Cloud for FREE"
msgstr ""

#: views/micro-cloud-modal.php:29
msgid "Help us secure our network and we’ll provide you with limited access to our premium features including our login firewall, IP Intelligence, and performance optimizer."
msgstr ""

#: views/micro-cloud-modal.php:32
msgid "Please note that some domains have very high brute force activity, which may cause Micro Cloud to run out of resources in under 24 hours. We will send an email when resources are fully utilized and the app reverts back to the free version. You may upgrade to one of our premium plans to prevent the app from reverting."
msgstr ""

#: views/micro-cloud-modal.php:43
msgid "How To Activate Micro Cloud"
msgstr ""

#: views/micro-cloud-modal.php:48
#: views/onboarding-popup.php:187
msgid "Please enter the email that will receive activation confirmation"
msgstr ""

#: views/micro-cloud-modal.php:53
#: views/onboarding-popup.php:142
#: views/onboarding-popup.php:190
#: views/tab-settings.php:562
#: views/tab-settings.php:586
msgid "Your email"
msgstr ""

#: views/micro-cloud-modal.php:61
#: views/onboarding-popup.php:196
msgid "I consent to registering my domain name <b>%s</b> with the Limit Login Attempts Reloaded cloud service."
msgstr ""

#: views/micro-cloud-modal.php:68
#: views/onboarding-popup.php:158
#: views/onboarding-popup.php:230
msgid "Continue"
msgstr ""

#: views/micro-cloud-modal.php:73
msgid "By signing up you agree to our <a href=\"%s\" class=\"llar_turquoise\">terms of service</a> and <a href=\"%s\" class=\"llar_turquoise\">privacy policy.</a>"
msgstr ""

#: views/micro-cloud-modal.php:82
#: views/onboarding-popup.php:223
msgid "The server is not working, try again later"
msgstr ""

#: views/micro-cloud-modal.php:90
msgid "Micro Cloud has been activated!"
msgstr ""

#: views/micro-cloud-modal.php:95
#: views/onboarding-popup.php:253
msgid "Go To Dashboard"
msgstr ""

#: views/onboarding-popup.php:35
#: views/onboarding-popup.php:60
msgid "Welcome"
msgstr ""

#: views/onboarding-popup.php:41
msgid "Notifications"
msgstr ""

#: views/onboarding-popup.php:47
msgid "Limited Upgrade"
msgstr ""

#: views/onboarding-popup.php:53
msgid "Completion"
msgstr ""

#: views/onboarding-popup.php:65
msgid "Add your Setup Code"
msgstr ""

#: views/onboarding-popup.php:68
msgid "Your Setup Code"
msgstr ""

#: views/onboarding-popup.php:70
msgid "Activate"
msgstr ""

#: views/onboarding-popup.php:77
msgid "The Setup Code can be found in your email if you have subscribed to premium"
msgstr ""

#: views/onboarding-popup.php:84
msgid "Not A Premium User?"
msgstr ""

#: views/onboarding-popup.php:87
msgid "We <b>highly recommend</b> upgrading to premium for the best protection against brute force attacks and unauthorized logins"
msgstr ""

#: views/onboarding-popup.php:91
msgid "Detect, counter, and deny unauthorized logins with IP Intelligence"
msgstr ""

#: views/onboarding-popup.php:94
msgid "Absorb failed login activity to improve site performance"
msgstr ""

#: views/onboarding-popup.php:97
msgid "Block IPs by country, premium support, and much more!"
msgstr ""

#: views/onboarding-popup.php:113
msgid "Yes, show me plan options"
msgstr ""

#: views/onboarding-popup.php:116
msgid "No, I don’t want advanced protection"
msgstr ""

#: views/onboarding-popup.php:122
#: views/onboarding-popup.php:162
#: views/onboarding-popup.php:227
msgid "Skip"
msgstr ""

#: views/onboarding-popup.php:137
msgid "Notification Settings"
msgstr ""

#: views/onboarding-popup.php:146
msgid "This email will receive notifications of unauthorized access to your website. You may turn this off in your settings."
msgstr ""

#: views/onboarding-popup.php:151
msgid "Sign me up for the LLAR newsletter to receive important security alerts, plugin updates, and helpful guides."
msgstr ""

#: views/onboarding-popup.php:176
msgid "Limited Upgrade (Free)"
msgstr ""

#: views/onboarding-popup.php:181
msgid "Help us secure our network and we’ll provide you with limited access to our premium features including our login firewall, IP intelligence, and performance optimizer (up to 1,000 requests monthly)."
msgstr ""

#: views/onboarding-popup.php:184
msgid "<b>Would you like to opt-in?</b>"
msgstr ""

#: views/onboarding-popup.php:206
msgid "Sign Me Up"
msgstr ""

#: views/onboarding-popup.php:212
msgid "You may opt-out of this program at any time. You accept our <a class=\"link__style_color_inherit llar_turquoise\" href=\"%s\" target=\"_blank\">terms of service</a> by participating in this program."
msgstr ""

#: views/onboarding-popup.php:219
msgid "Congrats! Your website is now activated for Micro Cloud. Account information has been emailed to you for your reference."
msgstr ""

#: views/onboarding-popup.php:245
msgid "Thank you for completing the setup"
msgstr ""

#: views/options-page.php:57
msgid "You have exhausted your monthly quota of free Micro Cloud requests. The plugin has now reverted to the free version. <a href=\"%s\" class=\"link__style_color_inherit\" target=\"_blank\">Upgrade to the premium</a> version today to maintain cloud protection and advanced features."
msgstr ""

#: views/options-page.php:72
msgid "Enjoying Micro Cloud? To prevent interruption of the cloud app, <a href=\"%s\" class=\"link__style_color_inherit\" target=\"_blank\">Upgrade to Premium</a> today"
msgstr ""

#: views/options-page.php:86
msgid "Do you want Limit Login Attempts Reloaded to provide the latest version automatically?"
msgstr ""

#: views/options-page.php:88
msgid "Yes, enable auto-update"
msgstr ""

#: views/options-page.php:92
msgid "No thanks"
msgstr ""

#: views/options-page.php:116
msgid "Account Login"
msgstr ""

#: views/options-page.php:159
msgid "Premium / Extensions"
msgstr ""

#: views/options-page.php:164
msgid "Failover"
msgstr ""

#: views/options-page.php:169
msgid "Automatic switch to free version when premium stops working (usually due to non-payment or exceeding monthly resource budget)."
msgstr ""

#: views/tab-dashboard.php:44
msgid "Enable Micro Cloud (FREE)"
msgstr ""

#: views/tab-dashboard.php:50
msgid "Help us secure our network by providing access to your login IP data."
msgstr ""

#: views/tab-dashboard.php:53
msgid "In return, receive access to our premium features up to 1,000 requests per month, and 100 for each subsequent month."
msgstr ""

#: views/tab-dashboard.php:56
msgid "Once the allocated requests are consumed, the premium app will switch back to the free version and reset the following month."
msgstr ""

#: views/tab-dashboard.php:67
#: views/tab-settings.php:95
msgid "Learn More"
msgstr ""

#: views/tab-dashboard.php:71
#: views/tab-settings.php:99
#: views/tab-settings.php:304
#: views/tab-settings.php:416
msgid "Get Started"
msgstr ""

#: views/tab-dashboard.php:75
msgid "* A request is utilized when our cloud app validates an IP before it is able to perform a login attempt."
msgstr ""

#: views/tab-dashboard.php:83
msgid "Premium Protection Disabled"
msgstr ""

#: views/tab-dashboard.php:87
msgid "As a free user, your local server is absorbing the traffic brought on by brute force attacks, potentially slowing down your website. Upgrade to Premium today to outsource these attacks through our cloud app, and slow down future attacks with advanced throttling."
msgstr ""

#: views/tab-dashboard.php:113
msgid "Tools"
msgstr ""

#: views/tab-dashboard.php:117
msgid "View lockouts logs, block or whitelist usernames or IPs, and more."
msgstr ""

#: views/tab-dashboard.php:132
msgid "Find the documentation and help you need."
msgstr ""

#: views/tab-dashboard.php:143
msgid "Global Options"
msgstr ""

#: views/tab-dashboard.php:147
msgid "Many options such as notifications, alerts, premium status, and more."
msgstr ""

#: views/tab-dashboard.php:183
msgid "Login Security Checklist"
msgstr ""

#: views/tab-dashboard.php:186
msgid "Recommended tasks to greatly improve the security of your website."
msgstr ""

#: views/tab-dashboard.php:193
msgid "Enable Email Notifications"
msgstr ""

#: views/tab-dashboard.php:197
msgid "<a class=\"link__style_unlink llar_turquoise\" href=\"%s\">Enable email notifications</a> to receive timely alerts and updates via email."
msgstr ""

#: views/tab-dashboard.php:205
msgid "Implement strong account policies"
msgstr ""

#: views/tab-dashboard.php:207
#: views/tab-dashboard.php:226
msgid "Check when done."
msgstr ""

#: views/tab-dashboard.php:210
msgid "<a class=\"link__style_unlink llar_turquoise\" href=\"%s\" target=\"_blank\">Read our guide</a> on implementing and enforcing strong password policies in your organization."
msgstr ""

#: views/tab-dashboard.php:218
msgid "Deny/Allow countries"
msgstr ""

#: views/tab-dashboard.php:221
msgid "Deny/Allow countries (Premium+ Users)"
msgstr ""

#: views/tab-dashboard.php:232
msgid "<a class=\"link__style_unlink llar_turquoise\" href=\"%s\" target=\"_blank\">Allow or Deny countries</a> to ensure only legitimate users login."
msgstr ""

#: views/tab-dashboard.php:240
msgid "Turn on plugin auto-updates"
msgstr ""

#: views/tab-dashboard.php:244
msgid "Enable automatic updates to ensure that the plugin stays current with the latest software patches and features."
msgstr ""

#: views/tab-dashboard.php:246
msgid "<a class=\"link__style_unlink llar_turquoise\" href=\"#llar_auto_update_choice\">Enable automatic updates</a> to ensure that the plugin stays current with the latest software patches and features."
msgstr ""

#: views/tab-dashboard.php:257
msgid "Upgrade to our premium version for advanced protection."
msgstr ""

#: views/tab-dashboard.php:263
msgid "<a class=\"link__style_unlink llar_turquoise\" href=\"%s\" target=\"_blank\">Upgrade to our premium</a> version for advanced protection."
msgstr ""

#: views/tab-debug.php:58
msgid "Debug Info"
msgstr ""

#: views/tab-debug.php:65
msgid "Copy the contents of the window and provide to support."
msgstr ""

#: views/tab-debug.php:70
msgid "Version"
msgstr ""

#: views/tab-debug.php:77
msgid "Start Over"
msgstr ""

#: views/tab-debug.php:82
msgid "You can start over the onboarding process by clicking this button. All existing data will remain unchanged."
msgstr ""

#: views/tab-debug.php:90
msgid "Reset"
msgstr ""

#: views/tab-help.php:20
msgid "Upgrade Now to Access Premium Support"
msgstr ""

#: views/tab-help.php:23
msgid "Our technical support team is available by email to help<br>with any questions."
msgstr ""

#: views/tab-help.php:28
msgid "Upgrade To Premium"
msgstr ""

#: views/tab-help.php:34
msgid "Free Support"
msgstr ""

#: views/tab-help.php:37
msgid "Support for free customers is available via our forums page on WordPress.org.<br>The majority of requests <b>receive an answer within a few days</b>."
msgstr ""

#: views/tab-help.php:42
msgid "Go To Support Forums"
msgstr ""

#: views/tab-help.php:51
msgid "GDPR Information"
msgstr ""

#: views/tab-help.php:55
msgid "Software Documentation"
msgstr ""

#: views/tab-help.php:62
msgid "All Documentation"
msgstr ""

#: views/tab-help.php:68
msgid "Cloud Service & Security"
msgstr ""

#: views/tab-help.php:70
msgid "Questions regarding the cloud service including how to activate, logs and storage, and compliance."
msgstr ""

#: views/tab-help.php:76
msgid "Technical Questions"
msgstr ""

#: views/tab-help.php:78
msgid "Popular technical questions about the service including admin blocking, definitions, and email notifications."
msgstr ""

#: views/tab-help.php:83
msgid "Accounts & Billing"
msgstr ""

#: views/tab-help.php:85
msgid "Questions regarding updating billing info, cancellation, and expiration."
msgstr ""

#: views/tab-help.php:91
msgid "Pre-sales Questions"
msgstr ""

#: views/tab-help.php:93
msgid "Questions regarding premium software sales."
msgstr ""

#: views/tab-help.php:101
msgid "Top Topics and Questions"
msgstr ""

#: views/tab-help.php:107
msgid "How do I know if I'm under attack?"
msgstr ""

#: views/tab-help.php:113
msgid "How can I tell that the premium plugin is working?"
msgstr ""

#: views/tab-help.php:119
msgid "What do I do if the admin gets blocked?"
msgstr ""

#: views/tab-help.php:125
msgid "Why am I still seeing login attempts even after the IP got blocked?"
msgstr ""

#: views/tab-help.php:131
msgid "Could these failed login attempts be fake?"
msgstr ""

#: views/tab-help.php:137
msgid "How does the login firewall work?"
msgstr ""

#: views/tab-help.php:143
msgid "What happens if my site exceeds the request limits in the plan?"
msgstr ""

#: views/tab-help.php:149
msgid "What do I do if all users get blocked?"
msgstr ""

#: views/tab-help.php:155
msgid "I just installed LLAR and I'm already getting several failed login attempts"
msgstr ""

#: views/tab-help.php:161
msgid "What URLs are being attacked and protected?"
msgstr ""

#: views/tab-help.php:176
msgid "Our technical support team is available by email to help with any questions."
msgstr ""

#: views/tab-help.php:181
msgid "Contact Support"
msgstr ""

#: views/tab-logs-custom.php:26
msgid "Your Micro Cloud plan has exhausted its requests for the month, which is required to operate the Login Firewall."
msgstr ""

#: views/tab-logs-custom.php:31
msgid "You can <a href=\"%s\" class=\"link__style_color_inherit llar_bold\" target=\"_blank\">Upgrade to Premium</a> to increase requests."
msgstr ""

#: views/tab-logs-custom.php:36
msgid "Or"
msgstr ""

#: views/tab-logs-custom.php:39
msgid "Switch to the failover to access IP management tools with the free version."
msgstr ""

#: views/tab-logs-local.php:35
msgid "Statistics"
msgstr ""

#: views/tab-logs-local.php:45
msgid "Total lockouts"
msgstr ""

#: views/tab-logs-local.php:50
msgid "Reset Counter"
msgstr ""

#: views/tab-logs-local.php:53
msgid "%d lockout since last reset"
msgid_plural "%d lockouts since last reset"
msgstr[0] ""
msgstr[1] ""

#: views/tab-logs-local.php:57
msgid "No lockouts yet"
msgstr ""

#: views/tab-logs-local.php:64
msgid "Active lockouts"
msgstr ""

#: views/tab-logs-local.php:68
msgid "Restore Lockouts"
msgstr ""

#: views/tab-logs-local.php:71
msgid "%d IP is currently blocked from trying to log in"
msgstr ""

#: views/tab-logs-local.php:88
msgid "Safelist"
msgstr ""

#: views/tab-logs-local.php:93
#: views/tab-logs-local.php:116
msgid "One IP or IP range (*******-*******) per line"
msgstr ""

#: views/tab-logs-local.php:101
#: views/tab-logs-local.php:124
msgid "One Username per line"
msgstr ""

#: views/tab-logs-local.php:111
msgid "Denylist"
msgstr ""

#: views/tab-logs-local.php:132
msgid "Automate your denylist with IP intelligence when you <a href=\"%s\" class=\"unlink link__style_unlink\" target=\"_blank\">upgrade to premium</a>."
msgstr ""

#: views/tab-logs-local.php:142
#: views/tab-settings.php:467
#: views/tab-settings.php:807
msgid "Save Settings"
msgstr ""

#: views/tab-logs-local.php:151
msgid "Upgrade To Premium For Our Login Firewall"
msgstr ""

#: views/tab-logs-local.php:154
#: views/tab-logs-local.php:260
#: views/tab-settings.php:296
#: views/tab-settings.php:408
msgid "Try For FREE"
msgstr ""

#: views/tab-logs-local.php:161
msgid "Identify & Counter New Threats With IP Intelligence"
msgstr ""

#: views/tab-logs-local.php:167
msgid "Access Active Databases Of Malicious IPs To Bolster Defenses"
msgstr ""

#: views/tab-logs-local.php:173
msgid "Unblock The Blocked Admin With Ease"
msgstr ""

#: views/tab-logs-local.php:179
msgid "Deny IPs By Country"
msgstr ""

#: views/tab-logs-local.php:194
msgid "Lockout log"
msgstr ""

#: views/tab-logs-local.php:202
msgid "Clear Log"
msgstr ""

#: views/tab-logs-local.php:206
msgid "<a href=\"%s\" class=\"unlink link__style_unlink\" target=\"_blank\">Upgrade today</a> to optimize or unload your DB by moving logs to the cloud."
msgstr ""

#: views/tab-logs-local.php:217
msgid "Date"
msgstr ""

#: views/tab-logs-local.php:218
msgctxt "Internet address"
msgid "IP"
msgstr ""

#: views/tab-logs-local.php:219
msgid "Tried to log in as"
msgstr ""

#: views/tab-logs-local.php:226
msgid "F d, Y H:i"
msgstr ""

#: views/tab-logs-local.php:231
msgid " lockouts"
msgstr ""

#: views/tab-logs-local.php:240
msgid "Unlock"
msgstr ""

#: views/tab-logs-local.php:243
msgid "Unlocked"
msgstr ""

#: views/tab-logs-local.php:257
msgid "Upgrade Today For Enhanced Logs & IP Intelligence"
msgstr ""

#: views/tab-logs-local.php:267
msgid "Enhanced Logs Tell You Exactly Which IPs Are Attempting Logins"
msgstr ""

#: views/tab-logs-local.php:273
msgid "Identify & Counter New Threats With Ease"
msgstr ""

#: views/tab-logs-local.php:279
msgid "Automatically Add Malicious IPs To Your Deny List"
msgstr ""

#: views/tab-logs-local.php:285
msgid "Unblock The Blocked Admins Effortlessly"
msgstr ""

#: views/tab-premium.php:30
msgid "Limit Login Attempts Reloaded <strong>Micro Cloud</strong>"
msgstr ""

#: views/tab-premium.php:32
msgid "Limit Login Attempts Reloaded <strong>Premium</strong>"
msgstr ""

#: views/tab-premium.php:38
msgid "Full feature list"
msgstr ""

#: views/tab-premium.php:43
msgid "Pre-sales FAQs"
msgstr ""

#: views/tab-premium.php:48
msgid "Ask a pre-sales question"
msgstr ""

#: views/tab-premium.php:53
msgid "Support"
msgstr ""

#: views/tab-premium.php:61
msgid "Get It Here"
msgstr ""

#: views/tab-premium.php:68
msgid "You are currently using the free version of <strong>Limit Login Attempts Reloaded</strong>."
msgstr ""

#: views/tab-premium.php:70
msgid "If you purchased a premium plan, check your email for setup instructions (Setup Code included)"
msgstr ""

#: views/tab-premium.php:74
msgid "You are currently using Micro Cloud, which provides access to premium cloud app on a limited basis. To prevent interruption, upgrade to one of our paid plans below."
msgstr ""

#: views/tab-premium.php:76
msgid "You are currently using the premium version of Limit Login Attempts Reloaded."
msgstr ""

#: views/tab-premium.php:85
msgid "Why Should I Consider Premium?"
msgstr ""

#: views/tab-premium.php:88
msgid "Although the free version offers basic protection, the premium version includes an important feature called <b>IP Intelligence</b>. With IP intelligence, your website will be able to identify malicious IPs before they attempt a login, and absorb them into the cloud to save system resources. Your site will not only be more secure, but will operate at its optimal performance."
msgstr ""

#: views/tab-premium.php:95
msgid "Features comparison"
msgstr ""

#: views/tab-settings.php:64
msgid "App Settings"
msgstr ""

#: views/tab-settings.php:67
msgid "The app absorbs the main load caused by brute-force attacks, analyzes login attempts, and blocks unwanted visitors. It provides other service functions as well."
msgstr ""

#: views/tab-settings.php:73
msgid "Micro Cloud"
msgstr ""

#: views/tab-settings.php:78
msgid "Micro Cloud is a limited upgrade to our cloud app that provides complimentary access to our premium features"
msgstr ""

#: views/tab-settings.php:85
msgid "Help us secure our network by sharing your login IP data. In return, receive limited access to our premium features up to 1,000 requests for the first month, and 100 requests each subsequent month. Once requests are exceeded for a given month, the premium app will switch to FREE and reset the following month."
msgstr ""

#: views/tab-settings.php:88
msgid "* Requests are utilized when the cloud app validates an IP address before it is able to perform a login."
msgstr ""

#: views/tab-settings.php:107
msgid "Active App"
msgstr ""

#: views/tab-settings.php:112
msgid "Switches from free version (local) to premium (cloud)."
msgstr ""

#: views/tab-settings.php:120
msgid "Local (Free version)"
msgstr ""

#: views/tab-settings.php:125
msgid "Cloud App (Premium version)"
msgstr ""

#: views/tab-settings.php:132
msgid "Get advanced protection by <a href=\"%s\" class=\"unlink llar-upgrade-to-cloud\">upgrading to our Cloud App</a>."
msgstr ""

#: views/tab-settings.php:143
msgid "Local App"
msgstr ""

#: views/tab-settings.php:147
msgid "Lockout"
msgstr ""

#: views/tab-settings.php:152
msgid "Set lockout limits based on failed attempts."
msgstr ""

#: views/tab-settings.php:161
msgid "allowed retries"
msgstr ""

#: views/tab-settings.php:166
msgid "Number of failed attempts allowed before locking out."
msgstr ""

#: views/tab-settings.php:174
msgid "minutes lockout"
msgstr ""

#: views/tab-settings.php:179
msgid "Lockout time in minutes."
msgstr ""

#: views/tab-settings.php:186
msgid "lockouts increase lockout time to"
msgstr ""

#: views/tab-settings.php:190
msgid "hours"
msgstr ""

#: views/tab-settings.php:195
msgid "After the specified number of lockouts the lockout time will increase by specified hours."
msgstr ""

#: views/tab-settings.php:203
msgid "hours until retries are reset"
msgstr ""

#: views/tab-settings.php:208
msgid "Time in hours before blocks are removed."
msgstr ""

#: views/tab-settings.php:214
msgid "After a specific IP address fails to log in <b>%1$s</b> times, a lockout lasting <b>%2$s</b> minutes is activated. If additional failed attempts occur within <b>%3$s</b> hours and lead to another lockout, once their combined total hits <b>%4$s</b>, the <b>%2$s</b> minutes duration is extended to <b>%5$s</b> hours. The lockout will be lifted once <b>%3$s</b> hours have passed since the last lockout incident."
msgstr ""

#: views/tab-settings.php:225
msgid "Trusted IP Origins"
msgstr ""

#: views/tab-settings.php:230
msgid "Server variables containing IP addresses."
msgstr ""

#: views/tab-settings.php:240
msgid "Specify the origins you trust in order of priority, separated by commas. We strongly recommend that you <b>do not</b> use anything other than REMOTE_ADDR since other origins can be easily faked. Examples: HTTP_X_FORWARDED_FOR, HTTP_CF_CONNECTING_IP, HTTP_X_SUCURI_CLIENTIP"
msgstr ""

#: views/tab-settings.php:249
#: views/tab-settings.php:404
msgid "Why Use Our Premium Cloud App?"
msgstr ""

#: views/tab-settings.php:255
#: views/tab-settings.php:424
msgid "Absorb site load caused by attacks"
msgstr ""

#: views/tab-settings.php:261
#: views/tab-settings.php:430
msgid "Use intelligent IP denial/unblocking technology"
msgstr ""

#: views/tab-settings.php:268
#: views/tab-settings.php:436
msgid "Sync the allow/deny/pass lists between multiple domains"
msgstr ""

#: views/tab-settings.php:275
#: views/tab-settings.php:442
msgid "Get premium support"
msgstr ""

#: views/tab-settings.php:282
#: views/tab-settings.php:448
msgid "Run auto backups of access control lists, lockouts and logs"
msgstr ""

#: views/tab-settings.php:289
#: views/tab-settings.php:454
msgid "No contract - cancel anytime"
msgstr ""

#: views/tab-settings.php:300
#: views/tab-settings.php:412
msgid "Upgrade"
msgstr ""

#: views/tab-settings.php:311
msgid "Limit Login Attempts Reloaded Cloud App"
msgstr ""

#: views/tab-settings.php:312
#: views/tab-settings.php:317
msgid "Setup Code"
msgstr ""

#: views/tab-settings.php:322
msgid "This is the code you receive via email once you subscribe to the LLAR premium cloud app. (example xxxxxxxxxxxxx=yek?putes/1v/moc.stpmettanigoltimil.ipa)"
msgstr ""

#: views/tab-settings.php:330
msgid "Edit"
msgstr ""

#: views/tab-settings.php:339
msgid "Submit"
msgstr ""

#: views/tab-settings.php:344
msgid "Add this code to all websites in your network to sync protection (payment required for additional domains unless it's with an Agency plan's first tier)."
msgstr ""

#: views/tab-settings.php:351
msgid "Configuration"
msgstr ""

#: views/tab-settings.php:473
msgid "General Settings"
msgstr ""

#: views/tab-settings.php:476
msgid "These settings are independent of the apps."
msgstr ""

#: views/tab-settings.php:484
msgid "Let network sites use their own settings"
msgstr ""

#: views/tab-settings.php:485
msgid "If disabled, the global settings will be forcibly applied to the entire network."
msgstr ""

#: views/tab-settings.php:490
msgid "Use global settings"
msgstr ""

#: views/tab-settings.php:514
msgid "GDPR compliance"
msgstr ""

#: views/tab-settings.php:518
msgid "This makes the plugin <a href=\"%s\" class=\"unlink link__style_unlink\" target=\"_blank\">GDPR</a> compliant by showing a message on the login page. <a href=\"%s\" class=\"unlink llar-label\" target=\"_blank\">Read more</a>"
msgstr ""

#: views/tab-settings.php:525
msgid "GDPR message"
msgstr ""

#: views/tab-settings.php:530
msgid "This message will appear at the bottom of the login page."
msgstr ""

#: views/tab-settings.php:540
msgid "You can use a shortcode here to insert links, for example, a link to your Privacy Policy page. <br>The shortcode is: [llar-link url=\"https://example.com\" text=\"Privacy Policy\"]"
msgstr ""

#: views/tab-settings.php:547
#: views/tab-settings.php:552
msgid "Weekly Digest"
msgstr ""

#: views/tab-settings.php:559
#: views/tab-settings.php:583
msgid "Email to"
msgstr ""

#: views/tab-settings.php:564
msgid "Receive a weekly digest that includes a recap of your failed logins and lockout notifications. Premium users will be able to see additional data such as countries and IPs with most failed logins."
msgstr ""

#: views/tab-settings.php:571
msgid "Notify on lockout"
msgstr ""

#: views/tab-settings.php:576
msgid "Email address to which lockout notifications will be sent."
msgstr ""

#: views/tab-settings.php:586
msgid "after"
msgstr ""

#: views/tab-settings.php:589
msgid "lockouts"
msgstr ""

#: views/tab-settings.php:591
msgid "Test Email Notifications"
msgstr ""

#: views/tab-settings.php:598
msgid "It's not uncommon for web hosts to turn off emails for plugins as a security measure.<br>We've <a class=\"llar_bold link__style_color_inherit\" href=\"%s\" target=\"_blank\">created an article</a> to troubleshoot common email deliverability issues."
msgstr ""

#: views/tab-settings.php:606
msgid "Display top menu item"
msgstr ""

#: views/tab-settings.php:611
msgid "The LLAR plugin displays its item on the top navigation menu, which provides a shortcut to the plugin."
msgstr ""

#: views/tab-settings.php:618
#: views/tab-settings.php:635
#: views/tab-settings.php:666
msgid "(Save and reload this page to see the changes)"
msgstr ""

#: views/tab-settings.php:623
msgid "Display left menu item"
msgstr ""

#: views/tab-settings.php:628
msgid "The LLAR plugin displays its item on the left navigation menu, which provides a shortcut to the plugin."
msgstr ""

#: views/tab-settings.php:639
msgid "Hide Dashboard Widget"
msgstr ""

#: views/tab-settings.php:644
msgid "The LLAR dashboard widget provides a quick glance of your daily failed login activity on the main WordPress dashboard. You may hide this widget by checking this box."
msgstr ""

#: views/tab-settings.php:654
msgid "Display Menu Warning Icon"
msgstr ""

#: views/tab-settings.php:659
msgid "The warning badge is a red bubble icon displayed next to the LLAR logo on the main vertical navigation menu. It displays a warning if there were more than 100 attempts for a day."
msgstr ""

#: views/tab-settings.php:794
msgid "Test email has been sent!"
msgstr ""
