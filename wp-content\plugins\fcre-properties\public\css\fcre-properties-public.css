@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

*{box-sizing:border-box;}
.fcre-primary-bg{background-color:var(--fcre-primary-color);}
.fcre-primary-text{color:var(--fcre-primary-color);}
.fcre-secondary-bg{background-color:var(--fcre-secondary-color);}
.fcre-secondary-text{color:var(--fcre-secondary-color);}



/* === Container === */
.fcre-container,.fcre-container-fluid{width:100%; padding-right:15px; padding-left:15px; margin-right:auto; margin-left:auto;}
.fcre-container{max-width:100%;}
@media (min-width:576px){
  .fcre-container{max-width:540px;}
}
@media (min-width:768px){
  .fcre-container{max-width:720px;}
}
@media (min-width:992px){
  .fcre-container{max-width:960px;}
}
@media (min-width:1200px){
  .fcre-container{max-width:1140px;}
}
@media (min-width:1400px){
  .fcre-container{max-width:1320px;}
}
/* === Row === */
.fcre-row{display:flex; flex-wrap:wrap; margin-right:-15px; margin-left:-15px;}
/* === Columns (base styles) === */
[class*="fcre-col"]{position:relative; width:100%; padding-right:15px; padding-left:15px;}
.fcre-col{flex:1 0 0%;}

/* === fcre-col-1 to fcre-col-12 === */
.fcre-col-1   { flex: 0 0 auto; width: 8.333333%; }
.fcre-col-2   { flex: 0 0 auto; width: 16.666667%; }
.fcre-col-3   { flex: 0 0 auto; width: 25%; }
.fcre-col-4   { flex: 0 0 auto; width: 33.333333%; }
.fcre-col-5   { flex: 0 0 auto; width: 41.666667%; }
.fcre-col-6   { flex: 0 0 auto; width: 50%; }
.fcre-col-7   { flex: 0 0 auto; width: 58.333333%; }
.fcre-col-8   { flex: 0 0 auto; width: 66.666667%; }
.fcre-col-9   { flex: 0 0 auto; width: 75%; }
.fcre-col-10  { flex: 0 0 auto; width: 83.333333%; }
.fcre-col-11  { flex: 0 0 auto; width: 91.666667%; }
.fcre-col-12  { flex: 0 0 auto; width: 100%; }

/* === Responsive: SM (≥576px) === */
@media (min-width: 576px) {
  .fcre-col-sm-auto { flex: 0 0 auto; width: auto; }
  .fcre-col-sm-1   { flex: 0 0 auto; width: 8.333333%; }
  .fcre-col-sm-2   { flex: 0 0 auto; width: 16.666667%; }
  .fcre-col-sm-3   { flex: 0 0 auto; width: 25%; }
  .fcre-col-sm-4   { flex: 0 0 auto; width: 33.333333%; }
  .fcre-col-sm-5   { flex: 0 0 auto; width: 41.666667%; }
  .fcre-col-sm-6   { flex: 0 0 auto; width: 50%; }
  .fcre-col-sm-7   { flex: 0 0 auto; width: 58.333333%; }
  .fcre-col-sm-8   { flex: 0 0 auto; width: 66.666667%; }
  .fcre-col-sm-9   { flex: 0 0 auto; width: 75%; }
  .fcre-col-sm-10  { flex: 0 0 auto; width: 83.333333%; }
  .fcre-col-sm-11  { flex: 0 0 auto; width: 91.666667%; }
  .fcre-col-sm-12  { flex: 0 0 auto; width: 100%; }
}

/* === Responsive: MD (≥768px) === */
@media (min-width: 768px) {
  .fcre-col-md-auto { flex: 0 0 auto; width: auto; }
  .fcre-col-md-1   { flex: 0 0 auto; width: 8.333333%; }
  .fcre-col-md-2   { flex: 0 0 auto; width: 16.666667%; }
  .fcre-col-md-3   { flex: 0 0 auto; width: 25%; }
  .fcre-col-md-4   { flex: 0 0 auto; width: 33.333333%; }
  .fcre-col-md-5   { flex: 0 0 auto; width: 41.666667%; }
  .fcre-col-md-6   { flex: 0 0 auto; width: 50%; }
  .fcre-col-md-7   { flex: 0 0 auto; width: 58.333333%; }
  .fcre-col-md-8   { flex: 0 0 auto; width: 66.666667%; }
  .fcre-col-md-9   { flex: 0 0 auto; width: 75%; }
  .fcre-col-md-10  { flex: 0 0 auto; width: 83.333333%; }
  .fcre-col-md-11  { flex: 0 0 auto; width: 91.666667%; }
  .fcre-col-md-12  { flex: 0 0 auto; width: 100%; }
}

/* === Responsive: LG (≥992px) === */
@media (min-width: 992px) {
  .fcre-col-lg-auto { flex: 0 0 auto; width: auto; }
  .fcre-col-lg-1   { flex: 0 0 auto; width: 8.333333%; }
  .fcre-col-lg-2   { flex: 0 0 auto; width: 16.666667%; }
  .fcre-col-lg-3   { flex: 0 0 auto; width: 25%; }
  .fcre-col-lg-4   { flex: 0 0 auto; width: 33.333333%; }
  .fcre-col-lg-5   { flex: 0 0 auto; width: 41.666667%; }
  .fcre-col-lg-6   { flex: 0 0 auto; width: 50%; }
  .fcre-col-lg-7   { flex: 0 0 auto; width: 58.333333%; }
  .fcre-col-lg-8   { flex: 0 0 auto; width: 66.666667%; }
  .fcre-col-lg-9   { flex: 0 0 auto; width: 75%; }
  .fcre-col-lg-10  { flex: 0 0 auto; width: 83.333333%; }
  .fcre-col-lg-11  { flex: 0 0 auto; width: 91.666667%; }
  .fcre-col-lg-12  { flex: 0 0 auto; width: 100%; }
}

/* === Responsive: XL (≥1200px) === */
@media (min-width: 1200px) {
  .fcre-col-xl-auto { flex: 0 0 auto; width: auto; }
  .fcre-col-xl-1   { flex: 0 0 auto; width: 8.333333%; }
  .fcre-col-xl-2   { flex: 0 0 auto; width: 16.666667%; }
  .fcre-col-xl-3   { flex: 0 0 auto; width: 25%; }
  .fcre-col-xl-4   { flex: 0 0 auto; width: 33.333333%; }
  .fcre-col-xl-5   { flex: 0 0 auto; width: 41.666667%; }
  .fcre-col-xl-6   { flex: 0 0 auto; width: 50%; }
  .fcre-col-xl-7   { flex: 0 0 auto; width: 58.333333%; }
  .fcre-col-xl-8   { flex: 0 0 auto; width: 66.666667%; }
  .fcre-col-xl-9   { flex: 0 0 auto; width: 75%; }
  .fcre-col-xl-10  { flex: 0 0 auto; width: 83.333333%; }
  .fcre-col-xl-11  { flex: 0 0 auto; width: 91.666667%; }
  .fcre-col-xl-12  { flex: 0 0 auto; width: 100%; }
}

/* === Responsive: XXL (≥1400px) === */
@media (min-width: 1400px) {
  .fcre-col-xxl-auto { flex: 0 0 auto; width: auto; }
  .fcre-col-xxl-1   { flex: 0 0 auto; width: 8.333333%; }
  .fcre-col-xxl-2   { flex: 0 0 auto; width: 16.666667%; }
  .fcre-col-xxl-3   { flex: 0 0 auto; width: 25%; }
  .fcre-col-xxl-4   { flex: 0 0 auto; width: 33.333333%; }
  .fcre-col-xxl-5   { flex: 0 0 auto; width: 41.666667%; }
  .fcre-col-xxl-6   { flex: 0 0 auto; width: 50%; }
  .fcre-col-xxl-7   { flex: 0 0 auto; width: 58.333333%; }
  .fcre-col-xxl-8   { flex: 0 0 auto; width: 66.666667%; }
  .fcre-col-xxl-9   { flex: 0 0 auto; width: 75%; }
  .fcre-col-xxl-10  { flex: 0 0 auto; width: 83.333333%; }
  .fcre-col-xxl-11  { flex: 0 0 auto; width: 91.666667%; }
  .fcre-col-xxl-12  { flex: 0 0 auto; width: 100%; }
}


/* === Padding All Sides === */
.fcre-p-0 { padding: 0 !important; }
.fcre-p-1 { padding: 0.25rem !important; }
.fcre-p-2 { padding: 0.5rem !important; }
.fcre-p-3 { padding: 1rem !important; }
.fcre-p-4 { padding: 1.5rem !important; }
.fcre-p-5 { padding: 3rem !important; }

/* === Padding Top === */
.fcre-pt-0 { padding-top: 0 !important; }
.fcre-pt-1 { padding-top: 0.25rem !important; }
.fcre-pt-2 { padding-top: 0.5rem !important; }
.fcre-pt-3 { padding-top: 1rem !important; }
.fcre-pt-4 { padding-top: 1.5rem !important; }
.fcre-pt-5 { padding-top: 3rem !important; }

/* === Padding Bottom === */
.fcre-pb-0 { padding-bottom: 0 !important; }
.fcre-pb-1 { padding-bottom: 0.25rem !important; }
.fcre-pb-2 { padding-bottom: 0.5rem !important; }
.fcre-pb-3 { padding-bottom: 1rem !important; }
.fcre-pb-4 { padding-bottom: 1.5rem !important; }
.fcre-pb-5 { padding-bottom: 3rem !important; }

/* === Padding Start (left in LTR) === */
.fcre-ps-0 { padding-left: 0 !important; }
.fcre-ps-1 { padding-left: 0.25rem !important; }
.fcre-ps-2 { padding-left: 0.5rem !important; }
.fcre-ps-3 { padding-left: 1rem !important; }
.fcre-ps-4 { padding-left: 1.5rem !important; }
.fcre-ps-5 { padding-left: 3rem !important; }

/* === Padding End (right in LTR) === */
.fcre-pe-0 { padding-right: 0 !important; }
.fcre-pe-1 { padding-right: 0.25rem !important; }
.fcre-pe-2 { padding-right: 0.5rem !important; }
.fcre-pe-3 { padding-right: 1rem !important; }
.fcre-pe-4 { padding-right: 1.5rem !important; }
.fcre-pe-5 { padding-right: 3rem !important; }

/* === Padding X (Left + Right) === */
.fcre-px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.fcre-px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.fcre-px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.fcre-px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }
.fcre-px-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
.fcre-px-5 { padding-left: 3rem !important; padding-right: 3rem !important; }

/* === Padding Y (Top + Bottom) === */
.fcre-py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.fcre-py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.fcre-py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.fcre-py-3 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
.fcre-py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.fcre-py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }



@media (max-width:1399px){
  .fcre-container,.fcre-container-lg,.fcre-container-md,.fcre-container-sm,.fcre-container-xl,.fcre-container-xxl{max-width:100%;}
}





.fcre-signle-property-heading{padding-bottom: 25px;}
.fcre-signle-property-heading h1{
    display: inline-block;
    font-size: 32px;
    line-height: 32px;
    text-transform: uppercase;
    font-weight: 600;
    font-family: "Poppins", sans-serif;
    color: #dd0082;
}
.fcre-signle-property-heading p{
    font-size: 14px;
    line-height: 18px;
    margin: 0;
    font-weight: 500;
}



.fcre-single-property .fcre-tab-content{min-height:64vh;}
.fcre-tab-content{display:none; border-top:none;}
.fcre-tab-content.active{display:block;}



.custom-tabs{font-family: "Poppins", sans-serif; width:100%;}
.fcre-tab-buttons{display:flex; flex-wrap: wrap; gap:5px; border-bottom:2px solid #ccc; border-top:2px solid #ccc; justify-content:space-between !important;}

.fcre-tab-btn{
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.2s ease-in-out;
    color: #7d8896;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    border-top: 2px solid transparent;
    margin-bottom: -2px;
    margin-top: -2px;
}
.fcre-tab-btn.active,.fcre-tab-btn:hover{color:var(--fcre-secondary-color); border-color:var(--fcre-secondary-color);}
.fcre-tab-btn.disabled{opacity:0.5; cursor:not-allowed; pointer-events:none;}

.fcre-single-property-map-overview{height:500px;}
.fcre-single-property-map{height:500px;}
.fcre-tab-btn.active,.fcre-tab-titles li.active{border-color:var(--fcre-secondary-color); color:var(--fcre-secondary-color);}



.fcre-form-group > input[type="text"],.fcre-form-group > input[type="number"],.fcre-form-group > input[type="email"],.fcre-form-group > input[type="tel"],.fcre-form-group > input[type="date"],.fcre-form-group > input[type="time"],.fcre-form-group > input[type="url"],.fcre-form-group > input[type="week"],.fcre-form-group > input[type="color"],.fcre-form-group > input[type="password"],.fcre-form-group > select,.fcre-form-group > textarea{width:100%; padding:0.5rem 1rem; margin:0; box-sizing:border-box; border:1px solid #ced4da; border-radius:0.25rem; transition:all 0.3s ease; margin-bottom:0.5rem;}
.fcre-form-group > label{font-size:14px; display:block; margin-bottom:5px; font-weight:700; font-family:"Poppins",sans-serif; text-transform:uppercase;}
.fcre-range-input{display:flex; align-items:center; gap:5px;}
.fcre-range-input span{font-size:14px; width:14px; flex-shrink:0; line-height:14px;}


.fcre-single-property-overview {margin: 50px 0;}
.fcre-single-property-title{
    margin: 0 0 20px 0;
    display: inline-block;
    font-size: 22px;
    line-height: 26px;
    text-transform: uppercase;
    font-weight: 700;
    font-family: "Poppins", sans-serif;
    color: #dd0082;
}
.fcre-single-property-overview .fcre-single-property-overview-item{display:flex; justify-content:space-between; text-align:left; padding:5px 0; border-bottom:1px solid #d70081;}
.fcre-single-property-overview .fcre-single-property-overview-item-label{flex:1; font-weight:600; color:#000000;}
.fcre-single-property-overview .fcre-single-property-overview-item-value{color:#555555; flex:1; font-weight: 300;}


.fcre-single-property-description ul{margin-top: 15px;}
.fcre-single-property-description ul li{
    position: relative;
    padding-left: 16px;
    font-size: 16px;
}
.fcre-single-property-description ul li:before{
    content: '-';
    color: #e20984;
    font-size: 22px;
    line-height: 22px;
    font-weight: 700;
    position: absolute;
    top: 0;
    left: 0;
}















.fcre-filter-wrapper{background: #f7f7f7; margin-bottom: 15px;}
.fcre-hide{display:none;}
.fcre-show{display:block;}
.fcre-btn-download-flyer{width:100%;}
.fcre-btn-download-om{width:100%;}
.fcre-btn-agreement{width:100%;}
.fcre-btn-virtual-tour{width:100%;}
.fcre-btn-property-video{width:100%;}
/* Disabled/grayed-out button styles for confidentiality agreement required */

.fcre-btn.fcre-btn-disabled,.fcre-btn-primary.fcre-btn-disabled{background-color:#cccccc !important; border-color:#cccccc !important; color:#666666 !important; cursor:not-allowed !important; opacity:0.6; pointer-events:none;}
.fcre-btn.fcre-btn-disabled:hover,.fcre-btn-primary.fcre-btn-disabled:hover{background-color:#cccccc !important; border-color:#cccccc !important; color:#666666 !important;}
.agreement-message{border:1px solid #FFC107; padding:10px; background:#fff7e5; border-radius:5px;}
.agreement-message a{font-weight:bold;}
.w-full{width:100%;}
.fcre-tab-tooltip{position:relative; padding:10px 20px; background:none; border:none; cursor:pointer; font-weight:bold; transition:all 0.2s ease-in-out; color:#7d8896; background-color:transparent; border:none; border-bottom:2px solid transparent;}
.fcre-tab-tooltip .tooltip-text{visibility:hidden; width:max-content; background-color:#333333; color:#fff; text-align:center; padding:6px 10px; font-size:14px; border-radius:5px; position:absolute; z-index:1; bottom:10px; left:50%; transform:translateX(-50%); opacity:0; transition:opacity 0.3s; white-space:nowrap;}
.fcre-tab-tooltip:hover .tooltip-text{visibility:visible; opacity:1;}
.fcre-tab-tooltip .tooltip-text::after{content:""; position:absolute; top:100%; /* Bottom of tooltip box */

  left:50%; transform:translateX(-50%); border-width:6px; border-style:solid; border-color:#333 transparent transparent transparent; /* Arrow pointing down */}
.fcre-multiselect-wrapper{width:100%; position:relative;}
.fcre-multiselect-wrapper .fcre-filter-label{
    font-size: 14px;
    display: block;
    margin-bottom: 5px;
    font-weight: 700;
    font-family: "Poppins", sans-serif;
    text-transform: uppercase;
}
.fcre-multiselect-wrapper .filter-select{position:relative; border:1px solid #ced4da; width:100%; background:#ffffff; padding:0.7rem 0.3rem; border-radius:4px;}
.fcre-multiselect-wrapper .filter-select-arrow{position:absolute; top:17px; right:5px; border-top:6px solid #a7a7a7; border-left:6px solid transparent; border-bottom:6px solid transparent; border-right:6px solid transparent; z-index:1;}
.fcre-multiselect-wrapper .filter-onclick{position:absolute; top:0; left:0; width:100%; background:transparent; cursor:pointer; z-index:1; height:40px;}
.fcre-multiselect-wrapper .filter-dropdown{background:#ffffff; position:absolute; width:100%; top:34px; left:0; z-index:3333; border:1px solid #a7a7a7;}
.fcre-multiselect-wrapper .filter-dropdown-area{padding:10px;}
.fcre-multiselect-wrapper .filter-placeholder{font-size:12px; display:block; width:100%; padding:0 10px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.fcre-multiselect-wrapper ul.fcre-dropdown{margin:0; padding:0;}
.fcre-multiselect-wrapper .fcre-dropdown li{list-style:none; line-height:18px;}
.fcre-multiselect-wrapper .filter-dropdown-area label{font-size:14px;}
.fcre-multiselect-wrapper .filter-placeholder{display:block; width:100%; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.leaflet-cluster-anim .leaflet-marker-icon,.leaflet-cluster-anim .leaflet-marker-shadow{-webkit-transition:-webkit-transform 0.3s ease-out,opacity 0.3s ease-in; -moz-transition:-moz-transform 0.3s ease-out,opacity 0.3s ease-in; -o-transition:-o-transform 0.3s ease-out,opacity 0.3s ease-in; transition:transform 0.3s ease-out,opacity 0.3s ease-in;}
.leaflet-cluster-spider-leg{/* stroke-dashoffset (duration and function) should match with leaflet-marker-icon transform in order to track it exactly */


-webkit-transition:-webkit-stroke-dashoffset 0.3s ease-out,-webkit-stroke-opacity 0.3s ease-in; -moz-transition:-moz-stroke-dashoffset 0.3s ease-out,-moz-stroke-opacity 0.3s ease-in; -o-transition:-o-stroke-dashoffset 0.3s ease-out,-o-stroke-opacity 0.3s ease-in; transition:stroke-dashoffset 0.3s ease-out,stroke-opacity 0.3s ease-in;}
.marker-cluster-small{background-color:rgba(181,226,140,0.6);}
.marker-cluster-small div{background-color:rgba(110,204,57,0.6);}
.marker-cluster-medium{background-color:rgba(241,211,87,0.6);}
.marker-cluster-medium div{background-color:rgba(240,194,12,0.6);}
.marker-cluster-large{background-color:rgba(253,156,115,0.6);}
.marker-cluster-large div{background-color:rgba(241,128,23,0.6);}
/* IE 6-8 fallback colors */

.leaflet-oldie .marker-cluster-small{background-color:rgb(181,226,140);}
.leaflet-oldie .marker-cluster-small div{background-color:rgb(110,204,57);}
.leaflet-oldie .marker-cluster-medium{background-color:rgb(241,211,87);}
.leaflet-oldie .marker-cluster-medium div{background-color:rgb(240,194,12);}
.leaflet-oldie .marker-cluster-large{background-color:rgb(253,156,115);}
.leaflet-oldie .marker-cluster-large div{background-color:rgb(241,128,23);}
.marker-cluster{background-clip:padding-box; border-radius:20px;}
.marker-cluster div{width:30px; height:30px; margin-left:5px; margin-top:5px; text-align:center; border-radius:15px; font:12px "Helvetica Neue",Arial,Helvetica,sans-serif;}
.marker-cluster span{line-height:30px;}
#fcre-listing-map-container{width:100%; height:900px; border:1px solid #ccc;}
#fcre-listing-map-container .leaflet-popup-content{margin:-12px !important; width:450px !important;}
#fcre-listing-map-container .leaflet-popup-content p{margin:0; font-size:14px; line-height:21px;}
#progress{display:none; position:absolute; z-index:1000; left:400px; top:300px; width:200px; height:20px; margin-top:-20px; margin-left:-100px; background-color:#fff; background-color:rgba(255,255,255,0.7); border-radius:4px; padding:2px;}
#progress-bar{width:0; height:100%; background-color:#76A6FC; border-radius:4px;}
.fcre-results{height:900px; max-height:100%; overflow-y:scroll;}

.fcre-listing-box {
    position: relative;
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center; /* key change */
    border: 2px solid rgba(0, 0, 0, 0.1);
    padding: 5px;
    min-height: 160px; /* optional safeguard */
}

.fcre-listing-box:after{content:""; display:block; height:3px; width:0; margin:0; position:absolute; bottom:-2px; left:0; transition:all 0.3s ease; z-index:1;}
.fcre-listing-box:hover:after{background:var(--fcre-primary-color); width:100%;}
.fcre-listing-link{text-decoration:none;}
.fcre-property-card:hover,.snap-cre-property-card:hover{border-color:var(--fcre-secondary-color);}
.fcre-listing-image {
    overflow: hidden;
    flex-shrink: 0; /* prevent image from shrinking */
    width: 150px;
    height: 150px;
}
.fcre-listing-box .fcre-listing-image img {
    transition: all 0.3s ease;
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.fcre-listing-box:hover .fcre-listing-image img{transform:scale(1.1);}
.fcre-listing-content {
    flex: 1;
    padding-right: 15px;
    position: relative;
}
.fcre-listing-box h2{
    font-size: 14px;
    line-height: 18px;
    text-transform: uppercase;
    font-weight: 600;
    font-family: "Poppins", sans-serif;
    color: #dd0082;
}
.fcre-listing-content h4{
    padding: 5px 5px 4px 5px;
    display: inline-block;
    font-size: 10px;
    line-height: 10px;
    text-transform: uppercase;
    color: #000000;
    background: var(--fcre-primary-color);
    font-weight: 700;
    letter-spacing: .5px;
}

.fcre-listing-content p{
    font-size: 14px;
    line-height: 18px;
    margin: 0;
    font-weight: 500;
}

.fcre-listing-content p span.fcre-address-highlight {
    font-size: 12px;
    display: block;
    color: #999999;
}


.fcre-btn{display:inline-flex; align-items:center; justify-content:center; justify-content:start; padding:0.5rem 1rem; font-size:1rem; border-radius:0.25rem; cursor:pointer; border:1px solid transparent; transition:all 0.3s ease; margin-right:0.5rem; margin-bottom:0.5rem; background-color:var(--fcre-button-color); border-color:var(--fcre-button-color); color:var(--fcre-button-text-color) !important;}
.fcre-btn-primary,.fcre-btn,.fcre-filter-btn,.fcre-submit-btn,.filter-submit,button.fcre-btn-primary,input[type=submit].fcre-btn{background-color:var(--fcre-button-color); border-color:var(--fcre-button-color); color:var(--fcre-button-text-color);}
.fcre-btn-primary:hover,.fcre-btn:hover,.fcre-filter-btn:hover,.fcre-submit-btn:hover,.filter-submit:hover,button.fcre-btn-primary:hover,input[type=submit].fcre-btn:hover{background-color:var(--fcre-button-hover-color); border-color:var(--fcre-button-hover-color); color:var(--fcre-button-hover-text-color);}
.fcre-property-link,.fcre-property-title a,.property-link,.snap-cre-property-card .property-link{color:var(--fcre-primary-color);}
.fcre-property-link:hover,.fcre-property-title a:hover,.property-link:hover,.snap-cre-property-card .property-link:hover{color:var(--fcre-button-hover-color);}
.fcre-icon-btn i{margin-right:0.5rem;}
.fcre-form-control{display:block; width:100%; padding:0.5rem 1rem; font-size:1rem; border-radius:0.25rem; border:1px solid #ced4da; transition:all 0.3s ease;}
.fcre-form-control:focus{outline:none; border-color:#86b7fe; box-shadow:0 0 0 0.25rem rgba(13,110,253,.25);}
.fcre-form-control:focus,.filter-field input:focus,.filter-field select:focus{border-color:var(--fcre-primary-color);}
.fcre-form-control::placeholder{color:#6c757d; opacity:1;}
.fcre-form-control:focus::placeholder{color:transparent;}
.fcre-form-control:disabled{background-color:#e9ecef; opacity:1;}
.fcre-status-badge,.property-status{background-color:var(--fcre-secondary-color);}
.leaflet-popup-content-wrapper{border-top:3px solid var(--fcre-primary-color);}
.leaflet-popup-content a.fcre-btn{font-size: 12px;padding: 5px 8px;margin-top: 10px;}
/* Filter wrapper styling */

		.snap-cre-filter-wrapper{border-left:4px solid var(--fcre-primary-color);}
/* Property grid cards */

		.snap-cre-property-card{border-bottom:2px solid transparent !important; transition:border-color 0.3s ease !important;}
.snap-cre-property-card:hover{border-bottom-color:var(--fcre-primary-color);}
.custom-modal{display:none; position:fixed; z-index:9999; padding-top:100px; left:0; top:0; width:100%; height:100%; overflow:auto; background-color:rgba(0,0,0,0.5);}
.custom-modal-content{background-color:#fff; margin:auto; padding:20px; border-radius:6px; width:100%; max-width:750px; position:relative; box-shadow:0 5px 15px rgba(0,0,0,0.3); margin-bottom:150px;}
.custom-modal-close{color:#aaa; position:absolute; top:10px; right:15px; font-size:28px; font-weight:bold; cursor:pointer;}
.custom-modal-close:hover{color:black;}
/* Prevent body scroll when modal is active */

body.modal-open{overflow:hidden;}
/* Large modal for video/virtual tour content */

.fcre-modal-large{max-width:90%; width:1200px;}
/* Modal body for embedded content */

.fcre-modal-body{padding:10px 0;}
.fcre-modal-body iframe{width:100%;}
/* Responsive iframe/video content in modals */

.fcre-modal-body iframe,.fcre-modal-body video,.fcre-modal-body embed,.fcre-modal-body object{max-width:100%; height:auto; min-height:400px;}
/* Responsive video wrapper */

.fcre-modal-body .video-wrapper{position:relative; padding-bottom:56.25%; /* 16:9 aspect ratio */

    height:0; overflow:hidden;}
.fcre-modal-body .video-wrapper iframe,.fcre-modal-body .video-wrapper video,.fcre-modal-body .video-wrapper embed,.fcre-modal-body .video-wrapper object{position:absolute; top:0; left:0; width:100%; height:100%;}
/* Mobile responsive adjustments */

@media (max-width:768px){
  .fcre-modal-large{max-width:95%; width:95%; margin:20px auto;}
  .custom-modal{padding-top:20px;}
  .fcre-modal-body iframe,.fcre-modal-body video,.fcre-modal-body embed,.fcre-modal-body object{min-height:250px;}
}
/*.fcre-photos-gallery,.fcre-site-plans-gallery,.fcre-floor-plans-gallery{display:flex; justify-content:center; align-items:center; flex-wrap:wrap;}*/







.fcre-single-property-demographics-item h2,.fcre-single-property-demographics-item p{margin:5px auto;}

a.pdf,a.docx,a.doc,a.xlsx,a.xls,a.zip,a.jpg,a.jpeg,a.png,a.bmp,a.gif,a.webp,a.avif,a.svg{display:block; width:fit-content; cursor:pointer;}
a.jpg,a.jpeg,a.png,a.bmp,a.gif,a.webp,a.avif,a.svg{background:url('../img/image.png') center left no-repeat; padding:10px 0 10px 30px; font-size:16px; text-decoration:none;}
a.pdf{background:url('../img/pdf.png') 0 8px no-repeat; padding:5px 0 5px 30px; font-size:16px; text-decoration:none;}
a.docx{background:url('../img/word.png') center left no-repeat; padding:5px 0 5px 30px; font-size:16px; text-decoration:none;}
a.doc{background:url('../img/word.png') center left no-repeat; padding:5px 0 5px 30px; font-size:16px; text-decoration:none;}
a.pptx{background:url('../img/pptx.png') center left no-repeat; padding:5px 0 5px 30px; font-size:16px; text-decoration:none;}
a.xlsx{background:url('../img/excel.png') center left no-repeat; padding:5px 0 5px 30px; font-size:16px; text-decoration:none;}
a.zip{background:url('../img/zip.png') center left no-repeat; padding:5px 0 5px 30px; font-size:16px; text-decoration:none;}
a.mp4{background:url('../img/video.png') center left no-repeat; padding:5px 0 5px 30px; font-size:16px; text-decoration:none;}

.fcre-single-property-sidebar-agents{margin-top:30px;}
.fcre-agent-list{list-style:none; padding:0; margin:5px 0 30px 0;}
.fcre-agent-item{display:flex; align-items:center; margin-bottom:10px;}
.fcre-agent-photo{flex:0 0 50px; margin-right:10px;}
.fcre-agent-photo img{width:50px; height:50px; object-fit:cover; border-radius:50%;}
.fcre-agent-info{flex:1;}
.fcre-agent-info h3{margin:0; font-size:16px;}
.fcre-agent-info p{margin:0; font-size:14px;}















@media (max-width:1600px){
    .fcre-listing-image {width: 120px;}
    .fcre-listing-content p {font-size: 13px; line-height: 18px;}
    .fcre-listing-content p span.fcre-address-highlight {font-size: 11px;}
}
@media (max-width:1440px){
    .fcre-listing-box {min-height:auto;}
    .fcre-listing-image {width: 100px; height:120px;}
    .fcre-listing-content p {font-size: 13px; line-height: 16px;}
    .fcre-listing-content p span.fcre-address-highlight {font-size: 11px;line-height:18px}
}


@media (max-width:1366px){
  .fcre-form-group > label, .fcre-multiselect-wrapper .fcre-filter-label{
      font-size: 14px;
  }
}
@media (max-width:1199px){
    .fcre-tab-btn{
        display: flex;
        flex-direction: column;
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .fcre-tab-btn {
        font-size: 13px;
        padding: 8px 12px;
    }
}
@media (max-width: 767px) {
    .fcre-tab-buttons {
        overflow-x: auto;
        white-space: nowrap;
        flex-wrap: nowrap;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
    }
    .fcre-tab-buttons::-webkit-scrollbar {
        display: none; /* Chrome, Safari */
    }

    .fcre-tab-btn {
        flex: 0 0 auto;
        padding: 8px 10px;
        font-size: 11px;
        text-align: center;
        line-height: 1.2;
        white-space: normal;
        min-width: 80px;
    }

    .fcre-tab-btn i {
        font-size: 14px;
        margin-bottom: 2px;
    }

    .fcre-tab-btn .tooltip-text {
        display: none;
    }
}
@media (max-width:768px){
    .fcre-multiselect-wrapper {margin-bottom: 0.5rem;}
}

@media (max-width:480px){
    .fcre-signle-property-heading h1 {font-size: 25px; line-height: 28px;}
}

