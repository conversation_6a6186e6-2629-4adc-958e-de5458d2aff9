<?php

/**
 * Template Name: Services
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
	while (have_posts()) : the_post();
		$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
		<div class="container-fluid mainBanner" style="background: url('<?php echo $image[0]; ?>') center center no-repeat; background-size: cover;">
			<div class="container-xl">
				<div class="row mainBannerText">
					<div class="col-12">
						<h2>Services</h2>
						<div class="breadcrumb">
							<a href="#">Home</a>
							<span><i class="fa-solid fa-angles-right mx-2"></i>Services</span>
						</div>
					</div>
				</div>
			</div>
		</div>
<?php
	endwhile;
}
?>


<div class="serviceAddSec1 py-6 d-flex align-items-center">
	<div class="container-xl">
		<div class="row justify-content-center">
			<?php
			$args = query_posts(
				array(
					'post_type' => 'our_services', // This is the name of your CPT
					'order' => 'ASC',
					'posts_per_page' => -1
				)
			);
			if (have_posts()) {
				while (have_posts()) : the_post();
					$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
			?>
				<div class="modal fade" id="serviceModal-<?php the_ID(); ?>" tabindex="-1" aria-labelledby="serviceModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
							<div class="modal-header cusHeader">
								<h1 class="modal-title fs-5" id="serviceModalLabel"><?php the_title(); ?></h1>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
							</div>
							<div class="modal-body">
								<?php the_content(); ?>
							</div>
						</div>
					</div>
				</div>

				<div class="col-lg-4 col-md-6 col-12">
					<div class="serviceAddSec1Col mb-3">
						<div class="serviceAddSec1ColIcon">
							<img src="<?php echo $image[0]; ?>" alt="">
						</div>
						<h3><?php the_title(); ?></h3>
						<a href="#serviceModal-<?php the_ID(); ?>" class="serviceAddSec1ColBtn" data-bs-toggle="modal">
							<i class="fa-solid fa-plus"></i> Read More
						</a>
					</div>
				</div>
			<?php
				endwhile;
			}
			wp_reset_query();
			?>
		</div>
	</div>
</div>


<?php get_footer(); ?>