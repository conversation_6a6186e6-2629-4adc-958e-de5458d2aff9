<?php
return array (
	'ZZ' => __('Unknown', 'limit-login-attempts-reloaded'),
	'RW' => __('Rwanda', 'limit-login-attempts-reloaded'),
	'SO' => __('Somalia', 'limit-login-attempts-reloaded'),
	'YE' => __('Yemen', 'limit-login-attempts-reloaded'),
	'IQ' => __('Iraq', 'limit-login-attempts-reloaded'),
	'SA' => __('Saudi Arabia', 'limit-login-attempts-reloaded'),
	'IR' => __('Iran', 'limit-login-attempts-reloaded'),
	'CY' => __('Cyprus', 'limit-login-attempts-reloaded'),
	'TZ' => __('Tanzania', 'limit-login-attempts-reloaded'),
	'SY' => __('Syria', 'limit-login-attempts-reloaded'),
	'AM' => __('Armenia', 'limit-login-attempts-reloaded'),
	'KE' => __('Kenya', 'limit-login-attempts-reloaded'),
	'CD' => __('DR Congo', 'limit-login-attempts-reloaded'),
	'DJ' => __('Djibouti', 'limit-login-attempts-reloaded'),
	'UG' => __('Uganda', 'limit-login-attempts-reloaded'),
	'CF' => __('Central African Republic', 'limit-login-attempts-reloaded'),
	'SC' => __('Seychelles', 'limit-login-attempts-reloaded'),
	'JO' => __('Jordan', 'limit-login-attempts-reloaded'),
	'LB' => __('Lebanon', 'limit-login-attempts-reloaded'),
	'KW' => __('Kuwait', 'limit-login-attempts-reloaded'),
	'OM' => __('Oman', 'limit-login-attempts-reloaded'),
	'QA' => __('Qatar', 'limit-login-attempts-reloaded'),
	'BH' => __('Bahrain', 'limit-login-attempts-reloaded'),
	'AE' => __('United Arab Emirates', 'limit-login-attempts-reloaded'),
	'IL' => __('Israel', 'limit-login-attempts-reloaded'),
	'TR' => __('Turkey', 'limit-login-attempts-reloaded'),
	'ET' => __('Ethiopia', 'limit-login-attempts-reloaded'),
	'ER' => __('Eritrea', 'limit-login-attempts-reloaded'),
	'EG' => __('Egypt', 'limit-login-attempts-reloaded'),
	'SD' => __('Sudan', 'limit-login-attempts-reloaded'),
	'GR' => __('Greece', 'limit-login-attempts-reloaded'),
	'BI' => __('Burundi', 'limit-login-attempts-reloaded'),
	'EE' => __('Estonia', 'limit-login-attempts-reloaded'),
	'LV' => __('Latvia', 'limit-login-attempts-reloaded'),
	'AZ' => __('Azerbaijan', 'limit-login-attempts-reloaded'),
	'LT' => __('Lithuania', 'limit-login-attempts-reloaded'),
	'SJ' => __('Svalbard and Jan Mayen', 'limit-login-attempts-reloaded'),
	'GE' => __('Georgia', 'limit-login-attempts-reloaded'),
	'MD' => __('Moldova', 'limit-login-attempts-reloaded'),
	'BY' => __('Belarus', 'limit-login-attempts-reloaded'),
	'FI' => __('Finland', 'limit-login-attempts-reloaded'),
	'AX' => __('Åland', 'limit-login-attempts-reloaded'),
	'UA' => __('Ukraine', 'limit-login-attempts-reloaded'),
	'MK' => __('North Macedonia', 'limit-login-attempts-reloaded'),
	'HU' => __('Hungary', 'limit-login-attempts-reloaded'),
	'BG' => __('Bulgaria', 'limit-login-attempts-reloaded'),
	'AL' => __('Albania', 'limit-login-attempts-reloaded'),
	'PL' => __('Poland', 'limit-login-attempts-reloaded'),
	'RO' => __('Romania', 'limit-login-attempts-reloaded'),
	'XK' => __('Kosovo', 'limit-login-attempts-reloaded'),
	'ZW' => __('Zimbabwe', 'limit-login-attempts-reloaded'),
	'ZM' => __('Zambia', 'limit-login-attempts-reloaded'),
	'KM' => __('Comoros', 'limit-login-attempts-reloaded'),
	'MW' => __('Malawi', 'limit-login-attempts-reloaded'),
	'LS' => __('Lesotho', 'limit-login-attempts-reloaded'),
	'BW' => __('Botswana', 'limit-login-attempts-reloaded'),
	'MU' => __('Mauritius', 'limit-login-attempts-reloaded'),
	'SZ' => __('Eswatini', 'limit-login-attempts-reloaded'),
	'RE' => __('Réunion', 'limit-login-attempts-reloaded'),
	'ZA' => __('South Africa', 'limit-login-attempts-reloaded'),
	'YT' => __('Mayotte', 'limit-login-attempts-reloaded'),
	'MZ' => __('Mozambique', 'limit-login-attempts-reloaded'),
	'MG' => __('Madagascar', 'limit-login-attempts-reloaded'),
	'AF' => __('Afghanistan', 'limit-login-attempts-reloaded'),
	'PK' => __('Pakistan', 'limit-login-attempts-reloaded'),
	'BD' => __('Bangladesh', 'limit-login-attempts-reloaded'),
	'TM' => __('Turkmenistan', 'limit-login-attempts-reloaded'),
	'TJ' => __('Tajikistan', 'limit-login-attempts-reloaded'),
	'LK' => __('Sri Lanka', 'limit-login-attempts-reloaded'),
	'BT' => __('Bhutan', 'limit-login-attempts-reloaded'),
	'IN' => __('India', 'limit-login-attempts-reloaded'),
	'MV' => __('Maldives', 'limit-login-attempts-reloaded'),
	'IO' => __('British Indian Ocean Territory', 'limit-login-attempts-reloaded'),
	'NP' => __('Nepal', 'limit-login-attempts-reloaded'),
	'MM' => __('Myanmar', 'limit-login-attempts-reloaded'),
	'UZ' => __('Uzbekistan', 'limit-login-attempts-reloaded'),
	'KZ' => __('Kazakhstan', 'limit-login-attempts-reloaded'),
	'KG' => __('Kyrgyzstan', 'limit-login-attempts-reloaded'),
	'TF' => __('French Southern Territories', 'limit-login-attempts-reloaded'),
	'HM' => __('Heard Island and McDonald Islands', 'limit-login-attempts-reloaded'),
	'CC' => __('Cocos [Keeling] Islands', 'limit-login-attempts-reloaded'),
	'PW' => __('Palau', 'limit-login-attempts-reloaded'),
	'VN' => __('Vietnam', 'limit-login-attempts-reloaded'),
	'TH' => __('Thailand', 'limit-login-attempts-reloaded'),
	'ID' => __('Indonesia', 'limit-login-attempts-reloaded'),
	'LA' => __('Laos', 'limit-login-attempts-reloaded'),
	'TW' => __('Taiwan', 'limit-login-attempts-reloaded'),
	'PH' => __('Philippines', 'limit-login-attempts-reloaded'),
	'MY' => __('Malaysia', 'limit-login-attempts-reloaded'),
	'CN' => __('China', 'limit-login-attempts-reloaded'),
	'HK' => __('Hong Kong', 'limit-login-attempts-reloaded'),
	'BN' => __('Brunei', 'limit-login-attempts-reloaded'),
	'MO' => __('Macao', 'limit-login-attempts-reloaded'),
	'KH' => __('Cambodia', 'limit-login-attempts-reloaded'),
	'KR' => __('South Korea', 'limit-login-attempts-reloaded'),
	'JP' => __('Japan', 'limit-login-attempts-reloaded'),
	'KP' => __('North Korea', 'limit-login-attempts-reloaded'),
	'SG' => __('Singapore', 'limit-login-attempts-reloaded'),
	'CK' => __('Cook Islands', 'limit-login-attempts-reloaded'),
	'TL' => __('East Timor', 'limit-login-attempts-reloaded'),
	'RU' => __('Russia', 'limit-login-attempts-reloaded'),
	'MN' => __('Mongolia', 'limit-login-attempts-reloaded'),
	'AU' => __('Australia', 'limit-login-attempts-reloaded'),
	'CX' => __('Christmas Island', 'limit-login-attempts-reloaded'),
	'MH' => __('Marshall Islands', 'limit-login-attempts-reloaded'),
	'FM' => __('Federated States of Micronesia', 'limit-login-attempts-reloaded'),
	'PG' => __('Papua New Guinea', 'limit-login-attempts-reloaded'),
	'SB' => __('Solomon Islands', 'limit-login-attempts-reloaded'),
	'TV' => __('Tuvalu', 'limit-login-attempts-reloaded'),
	'NR' => __('Nauru', 'limit-login-attempts-reloaded'),
	'VU' => __('Vanuatu', 'limit-login-attempts-reloaded'),
	'NC' => __('New Caledonia', 'limit-login-attempts-reloaded'),
	'NF' => __('Norfolk Island', 'limit-login-attempts-reloaded'),
	'NZ' => __('New Zealand', 'limit-login-attempts-reloaded'),
	'FJ' => __('Fiji', 'limit-login-attempts-reloaded'),
	'LY' => __('Libya', 'limit-login-attempts-reloaded'),
	'CM' => __('Cameroon', 'limit-login-attempts-reloaded'),
	'SN' => __('Senegal', 'limit-login-attempts-reloaded'),
	'CG' => __('Congo Republic', 'limit-login-attempts-reloaded'),
	'PT' => __('Portugal', 'limit-login-attempts-reloaded'),
	'LR' => __('Liberia', 'limit-login-attempts-reloaded'),
	'CI' => __('Ivory Coast', 'limit-login-attempts-reloaded'),
	'GH' => __('Ghana', 'limit-login-attempts-reloaded'),
	'GQ' => __('Equatorial Guinea', 'limit-login-attempts-reloaded'),
	'NG' => __('Nigeria', 'limit-login-attempts-reloaded'),
	'BF' => __('Burkina Faso', 'limit-login-attempts-reloaded'),
	'TG' => __('Togo', 'limit-login-attempts-reloaded'),
	'GW' => __('Guinea-Bissau', 'limit-login-attempts-reloaded'),
	'MR' => __('Mauritania', 'limit-login-attempts-reloaded'),
	'BJ' => __('Benin', 'limit-login-attempts-reloaded'),
	'GA' => __('Gabon', 'limit-login-attempts-reloaded'),
	'SL' => __('Sierra Leone', 'limit-login-attempts-reloaded'),
	'ST' => __('São Tomé and Príncipe', 'limit-login-attempts-reloaded'),
	'GI' => __('Gibraltar', 'limit-login-attempts-reloaded'),
	'GM' => __('Gambia', 'limit-login-attempts-reloaded'),
	'GN' => __('Guinea', 'limit-login-attempts-reloaded'),
	'TD' => __('Chad', 'limit-login-attempts-reloaded'),
	'NE' => __('Niger', 'limit-login-attempts-reloaded'),
	'ML' => __('Mali', 'limit-login-attempts-reloaded'),
	'EH' => __('Western Sahara', 'limit-login-attempts-reloaded'),
	'TN' => __('Tunisia', 'limit-login-attempts-reloaded'),
	'ES' => __('Spain', 'limit-login-attempts-reloaded'),
	'MA' => __('Morocco', 'limit-login-attempts-reloaded'),
	'MT' => __('Malta', 'limit-login-attempts-reloaded'),
	'DZ' => __('Algeria', 'limit-login-attempts-reloaded'),
	'FO' => __('Faroe Islands', 'limit-login-attempts-reloaded'),
	'DK' => __('Denmark', 'limit-login-attempts-reloaded'),
	'IS' => __('Iceland', 'limit-login-attempts-reloaded'),
	'GB' => __('United Kingdom', 'limit-login-attempts-reloaded'),
	'CH' => __('Switzerland', 'limit-login-attempts-reloaded'),
	'SE' => __('Sweden', 'limit-login-attempts-reloaded'),
	'NL' => __('Netherlands', 'limit-login-attempts-reloaded'),
	'AT' => __('Austria', 'limit-login-attempts-reloaded'),
	'BE' => __('Belgium', 'limit-login-attempts-reloaded'),
	'DE' => __('Germany', 'limit-login-attempts-reloaded'),
	'LU' => __('Luxembourg', 'limit-login-attempts-reloaded'),
	'IE' => __('Ireland', 'limit-login-attempts-reloaded'),
	'MC' => __('Monaco', 'limit-login-attempts-reloaded'),
	'FR' => __('France', 'limit-login-attempts-reloaded'),
	'AD' => __('Andorra', 'limit-login-attempts-reloaded'),
	'LI' => __('Liechtenstein', 'limit-login-attempts-reloaded'),
	'JE' => __('Jersey', 'limit-login-attempts-reloaded'),
	'IM' => __('Isle of Man', 'limit-login-attempts-reloaded'),
	'GG' => __('Guernsey', 'limit-login-attempts-reloaded'),
	'SK' => __('Slovakia', 'limit-login-attempts-reloaded'),
	'CZ' => __('Czechia', 'limit-login-attempts-reloaded'),
	'NO' => __('Norway', 'limit-login-attempts-reloaded'),
	'VA' => __('Vatican City', 'limit-login-attempts-reloaded'),
	'SM' => __('San Marino', 'limit-login-attempts-reloaded'),
	'IT' => __('Italy', 'limit-login-attempts-reloaded'),
	'SI' => __('Slovenia', 'limit-login-attempts-reloaded'),
	'ME' => __('Montenegro', 'limit-login-attempts-reloaded'),
	'HR' => __('Croatia', 'limit-login-attempts-reloaded'),
	'BA' => __('Bosnia and Herzegovina', 'limit-login-attempts-reloaded'),
	'AO' => __('Angola', 'limit-login-attempts-reloaded'),
	'NA' => __('Namibia', 'limit-login-attempts-reloaded'),
	'SH' => __('Saint Helena', 'limit-login-attempts-reloaded'),
	'BV' => __('Bouvet Island', 'limit-login-attempts-reloaded'),
	'BB' => __('Barbados', 'limit-login-attempts-reloaded'),
	'CV' => __('Cabo Verde', 'limit-login-attempts-reloaded'),
	'GY' => __('Guyana', 'limit-login-attempts-reloaded'),
	'GF' => __('French Guiana', 'limit-login-attempts-reloaded'),
	'SR' => __('Suriname', 'limit-login-attempts-reloaded'),
	'PM' => __('Saint Pierre and Miquelon', 'limit-login-attempts-reloaded'),
	'GL' => __('Greenland', 'limit-login-attempts-reloaded'),
	'PY' => __('Paraguay', 'limit-login-attempts-reloaded'),
	'UY' => __('Uruguay', 'limit-login-attempts-reloaded'),
	'BR' => __('Brazil', 'limit-login-attempts-reloaded'),
	'FK' => __('Falkland Islands', 'limit-login-attempts-reloaded'),
	'GS' => __('South Georgia and the South Sandwich Islands', 'limit-login-attempts-reloaded'),
	'JM' => __('Jamaica', 'limit-login-attempts-reloaded'),
	'DO' => __('Dominican Republic', 'limit-login-attempts-reloaded'),
	'CU' => __('Cuba', 'limit-login-attempts-reloaded'),
	'MQ' => __('Martinique', 'limit-login-attempts-reloaded'),
	'BS' => __('Bahamas', 'limit-login-attempts-reloaded'),
	'BM' => __('Bermuda', 'limit-login-attempts-reloaded'),
	'AI' => __('Anguilla', 'limit-login-attempts-reloaded'),
	'TT' => __('Trinidad and Tobago', 'limit-login-attempts-reloaded'),
	'KN' => __('St Kitts and Nevis', 'limit-login-attempts-reloaded'),
	'DM' => __('Dominica', 'limit-login-attempts-reloaded'),
	'AG' => __('Antigua and Barbuda', 'limit-login-attempts-reloaded'),
	'LC' => __('Saint Lucia', 'limit-login-attempts-reloaded'),
	'TC' => __('Turks and Caicos Islands', 'limit-login-attempts-reloaded'),
	'AW' => __('Aruba', 'limit-login-attempts-reloaded'),
	'VG' => __('British Virgin Islands', 'limit-login-attempts-reloaded'),
	'VC' => __('Saint Vincent and the Grenadines', 'limit-login-attempts-reloaded'),
	'MS' => __('Montserrat', 'limit-login-attempts-reloaded'),
	'MF' => __('Saint Martin', 'limit-login-attempts-reloaded'),
	'BL' => __('Saint Barthélemy', 'limit-login-attempts-reloaded'),
	'GP' => __('Guadeloupe', 'limit-login-attempts-reloaded'),
	'GD' => __('Grenada', 'limit-login-attempts-reloaded'),
	'KY' => __('Cayman Islands', 'limit-login-attempts-reloaded'),
	'BZ' => __('Belize', 'limit-login-attempts-reloaded'),
	'SV' => __('El Salvador', 'limit-login-attempts-reloaded'),
	'GT' => __('Guatemala', 'limit-login-attempts-reloaded'),
	'HN' => __('Honduras', 'limit-login-attempts-reloaded'),
	'NI' => __('Nicaragua', 'limit-login-attempts-reloaded'),
	'CR' => __('Costa Rica', 'limit-login-attempts-reloaded'),
	'VE' => __('Venezuela', 'limit-login-attempts-reloaded'),
	'EC' => __('Ecuador', 'limit-login-attempts-reloaded'),
	'CO' => __('Colombia', 'limit-login-attempts-reloaded'),
	'PA' => __('Panama', 'limit-login-attempts-reloaded'),
	'HT' => __('Haiti', 'limit-login-attempts-reloaded'),
	'AR' => __('Argentina', 'limit-login-attempts-reloaded'),
	'CL' => __('Chile', 'limit-login-attempts-reloaded'),
	'BO' => __('Bolivia', 'limit-login-attempts-reloaded'),
	'PE' => __('Peru', 'limit-login-attempts-reloaded'),
	'MX' => __('Mexico', 'limit-login-attempts-reloaded'),
	'PF' => __('French Polynesia', 'limit-login-attempts-reloaded'),
	'PN' => __('Pitcairn Islands', 'limit-login-attempts-reloaded'),
	'KI' => __('Kiribati', 'limit-login-attempts-reloaded'),
	'TK' => __('Tokelau', 'limit-login-attempts-reloaded'),
	'TO' => __('Tonga', 'limit-login-attempts-reloaded'),
	'WF' => __('Wallis and Futuna', 'limit-login-attempts-reloaded'),
	'WS' => __('Samoa', 'limit-login-attempts-reloaded'),
	'NU' => __('Niue', 'limit-login-attempts-reloaded'),
	'MP' => __('Northern Mariana Islands', 'limit-login-attempts-reloaded'),
	'GU' => __('Guam', 'limit-login-attempts-reloaded'),
	'PR' => __('Puerto Rico', 'limit-login-attempts-reloaded'),
	'VI' => __('U.S. Virgin Islands', 'limit-login-attempts-reloaded'),
	'UM' => __('U.S. Minor Outlying Islands', 'limit-login-attempts-reloaded'),
	'AS' => __('American Samoa', 'limit-login-attempts-reloaded'),
	'CA' => __('Canada', 'limit-login-attempts-reloaded'),
	'US' => __('United States', 'limit-login-attempts-reloaded'),
	'PS' => __('Palestine', 'limit-login-attempts-reloaded'),
	'RS' => __('Serbia', 'limit-login-attempts-reloaded'),
	'AQ' => __('Antarctica', 'limit-login-attempts-reloaded'),
	'SX' => __('Sint Maarten', 'limit-login-attempts-reloaded'),
	'CW' => __('Curaçao', 'limit-login-attempts-reloaded'),
	'BQ' => __('Bonaire, Sint Eustatius, and Saba', 'limit-login-attempts-reloaded'),
	'SS' => __('South Sudan', 'limit-login-attempts-reloaded'),
);