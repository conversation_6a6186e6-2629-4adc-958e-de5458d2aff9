jQuery(function($) {
    'use strict';

    function initializeSortable(metaKey) {
        const container = $('#' + metaKey);

        container.sortable({
            handle: '.fcre-repeater-drag',
            placeholder: 'fcre-sortable-placeholder',
            forcePlaceholderSize: true,
            items: '> .fcre-repeater-item',
            tolerance: 'pointer'
        });
    }

    function generateFieldHtml(metaKey, counter, field) {
        let html = `<div class="fcre-repeater-field"><label>${field.label}</label>`;

        if (field.type === 'text') {
            html += `<input type="text" name="${metaKey}[${counter}][${field.name}]" class="form-control">`;
        } else if (field.type === 'textarea') {
            html += `<textarea name="${metaKey}[${counter}][${field.name}]" class="form-control"></textarea>`;
        } else if (field.type === 'select') {
            html += `<select name="${metaKey}[${counter}][${field.name}]" class="form-control">`;
            if (field.options) {
                for (const key in field.options) {
                    html += `<option value="${key}">${field.options[key]}</option>`;
                }
            }
            html += `</select>`;
        } else if (field.type === 'wysiwyg') {
            html += `<textarea class="form-control wysiwyg-init" data-name="${metaKey}[${counter}][${field.name}]"></textarea>`;
        } else if (field.type === 'image') {
            html += `<div class="fcre-image-upload">
                        <input type="hidden" name="${metaKey}[${counter}][${field.name}]" value="">
                        <button type="button" class="button fcre-upload-button">Upload Image</button>
                        <div class="fcre-image-preview"></div>
                    </div>`;
        }

        html += `</div>`;
        return html;
    }

    function addRepeaterItem(metaKey) {
        const container = $('#' + metaKey);
        const fields = container.data('fields');
        const counter = container.find('.fcre-repeater-item').length;

        let itemHtml = `<div class="fcre-repeater-item">
                            <div class="fcre-repeater-drag">☰</div>
                            <div class="fcre-repeater-fields">`;

        fields.forEach(function(field) {
            itemHtml += generateFieldHtml(metaKey, counter, field);
        });

        itemHtml += `</div><div class="fcre-repeater-remove"><span class="icon-trash"></span></div></div>`;

        container.append(itemHtml);

        initializeWysiwyg();
        initializeSortable(metaKey);
    }

    function initializeWysiwyg() {
        $('.wysiwyg-init').each(function() {
            if (!$(this).hasClass('tinymce-initialized')) {
                const id = 'wysiwyg_' + Math.random().toString(36).substr(2, 9);
                $(this).attr('id', id).addClass('tinymce-initialized');

                tinymce.init({
                    selector: `#${id}`,
                    height: 150,
                    menubar: false,
                    toolbar: 'bold italic underline | alignleft aligncenter alignright | bullist numlist | link',
                    branding: false
                });
            }
        });
    }

    $(document).on('click', '.fcre-repeater-add', function(e) {
        e.preventDefault();
        const metaKey = $(this).data('meta-key');
        addRepeaterItem(metaKey);
    });

    $(document).on('click', '.fcre-repeater-remove', function() {
        if (confirm('Are you sure you want to remove this item?')) {
            $(this).closest('.fcre-repeater-item').remove();
        }
    });

    $(document).on('click', '.fcre-upload-button', function(e) {
        e.preventDefault();
        const button = $(this);
        const container = button.closest('.fcre-image-upload');
        const input = container.find('input[type="hidden"]');
        const preview = container.find('.fcre-image-preview');

        const frame = wp.media({
            title: 'Select or Upload Image',
            button: {
                text: 'Use this image'
            },
            multiple: false
        });

        frame.on('select', function() {
            const attachment = frame.state().get('selection').first().toJSON();
            input.val(attachment.id);
            preview.html('<img src="' + attachment.sizes.thumbnail.url + '" />');
            button.text('Change Image');
        });

        frame.open();
    });

    // Initialize existing repeaters
    $('.fcre-repeater-body').each(function() {
        initializeSortable($(this).attr('id'));
    });

    initializeWysiwyg();
});
