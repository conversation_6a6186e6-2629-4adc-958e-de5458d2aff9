<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit915a718651dbe9c9bf619aa70f02c756
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'Wpsec\\captcha\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Wpsec\\captcha\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit915a718651dbe9c9bf619aa70f02c756::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit915a718651dbe9c9bf619aa70f02c756::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit915a718651dbe9c9bf619aa70f02c756::$classMap;

        }, null, ClassLoader::class);
    }
}
