{"name": "godaddy-wordpress/styles", "description": "", "type": "wordpress-plugin", "license": "GPL-2.0-only", "authors": [{"name": "GoDaddy", "homepage": "https://godaddy.com"}], "require": {"php": ">=5.6"}, "config": {"platform": {"php": "5.6.20"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "autoload": {"psr-4": {"GoDaddy\\Styles\\": ""}}, "archive": {"exclude": ["*.lock", "package*", "README.md", "godaddy-styles.php", "src"]}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.2", "johnpbloch/wordpress-core": "^5.9", "phpcompatibility/phpcompatibility-wp": "^2.1", "phpunit/phpunit": "^5", "squizlabs/php_codesniffer": "^3.5", "wp-coding-standards/wpcs": "^2.3", "wp-phpunit/wp-phpunit": "^5.8", "yoast/phpunit-polyfills": "^1.0.1", "mikey179/vfsstream": "^1.6"}, "scripts": {"lint": "@php ./vendor/bin/phpcs", "lint:fix": "@php ./vendor/bin/phpcbf", "test": "@php ./vendor/bin/phpunit"}}