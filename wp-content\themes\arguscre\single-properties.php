<?php

/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package WordPress
 * @subpackage Twenty_Twenty_One
 * @since Twenty Twenty-One 1.0
 */

get_header();
$home_meta = get_field_objects(2);
?>

<?php $ids = get_post_meta($post->ID, 'vdw_gallery_id', true); ?>
<div class="container-fluid bottomHeader">
	<div class="container-xl">
		<div class="row">
			<div class="col-md-12 gen-rel">
				<ul id="image-gallery" class="gallery">
					<?php
					if ($ids) {
						foreach ($ids as $key => $value) {
							$imagelg = wp_get_attachment_image_src($value, 'single-post-thumbnail');
							$image = wp_get_attachment_image_src($value, 'single-post-thumbnail');
					?>
							<li data-thumb="<?php echo $image[0]; ?>">
								<img src="<?php echo $imagelg[0]; ?>" 
								width="1920" height="1080"
								style="aspect-ratio: 16/9; object-fit: cover; object-position: center;"
								class="img-fluid">
							</li>
					<?php
						}
					}
					?>
				</ul>
			</div>
		</div>
		<div class="row pt-5 pb-5">
			<div class="col-md-8 col-12">
				<div class="pSingleLeftSec">
					<h1><?php the_title(); ?></h1>
					<?php $citystate = get_field('citystate'); 
					if($citystate) : ?>
					<h4><?php echo $citystate; ?></h4>
					<?php endif; ?>
					
					<?php the_content(); ?>
				</div>
			</div>
			<div class="col-md-4 col-12">

				<div class="highlights">
					<h3>Highlights</h3>
					<?php
					$sale_price = get_field('sale_price');
					$lot_size = get_field('lot_size');
					$zoning = get_field('zoning');
					$country = get_field('country');
					?>
					<ul>
						<?php if ($sale_price) { ?>
							<li><strong>Sale Price: </strong><?php echo $sale_price; ?></li>
						<?php } ?>
						<?php if ($lot_size) { ?>
							<li><strong>Lot Size: </strong><?php echo $lot_size; ?></li>
						<?php } ?>
						<?php if ($zoning) { ?>
							<li><strong>Zoning: </strong><?php echo $zoning; ?></li>
						<?php } ?>
						<?php if ($country) { ?>
							<li><strong>Country: </strong><?php echo $country; ?></li>
						<?php } ?>
					</ul>
				</div>
				
				<div class="pSingleRightSec">
					<div class="pSingleRightInnerSec mb-2">
						<div><i class="fa-solid fa-file-pdf"></i></div>
						<div>
							<h4>Brochure</h4>
							<?php $brochure = get_field('download_om');?>
							<?php if($brochure) : ?>
							<a href="<?php echo $brochure; ?>">Download Brochure</a>
							<?php endif; ?>
						</div>
					</div>
					<div class="pSingleRightInnerSec">
						<div><i class="fa-solid fa-phone-volume"></i></div>
						<div>
							<h4>Call For Inquiries</h4>
							<a href="tel:<?= $home_meta['phone']['value'] ?>">Click Here</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php get_footer();?>
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<link rel="stylesheet" href="<?php echo esc_url(get_template_directory_uri()); ?>/assets/css/lightslider.css"/>
<script src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/js/lightslider.js"></script>
<script>
    $(document).ready(function () {
        $('#image-gallery').lightSlider({
            gallery: true,
            item: 1,
            slideMargin: 0,
            speed: 1000,
			pause: 5000,
            auto: true,
            loop: true,
            onSliderLoad: function () {
                $('#image-gallery').removeClass('cS-hidden');
            }
        });
    });
</script>