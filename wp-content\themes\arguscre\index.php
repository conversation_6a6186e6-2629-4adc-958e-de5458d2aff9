<?php

/**
 * Template Name: Home
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>

<?php
if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
        <div class="homeAddSec1">
            <div class="row">
                <div class="col-12 px-0 homeAddSec1Inner">
                    <video autoplay="" muted="" loop="" class="">
                        <source src="<?php echo $home_meta['home_additional_section_1_video']['value']; ?>" type="video/mp4">
                    </video>
                    <div class="container-xl">
                        <div class="row">
                            <div class="col-xl-8 col-lg-8 col-md-8 col-12">
                                <div class="px-xl-0 px-5 homeAddSec1InnerText">
                                    <?php the_content(); ?>
                                    <a href="<?= get_page_link(80) ?>" class="cusBtn cusBtn1 me-3">Our Company</a>
                                    <a href="<?= get_page_link(115) ?>" class="cusBtn cusBtn2">Properties</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
}
?>

<div class="homeAddSec2 pb-lg-6 pb-5">
    <div class="row">
        <div class="col-xxl-5 col-xl-4 col-lg-3 col-12"></div>
        <div class="col-xxl-7 col-xl-8 col-lg-9 col-12">
            <div class="row">
                <div class="col-lg-4 px-0">
                    <a href="<?php bloginfo('url')?>/about">
                        <div class="homeAddSec2Col1">
                            <h2>Our Team</h2>
                            <p>Meet the experienced professionals driving our mission. With deep industry expertise and local insight, our team is committed to delivering impactful real estate solutions.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 px-0">
                    <a href="#testimonial-sec">
                        <div class="homeAddSec2Col2">
                            <h2>Testimonials</h2>
                            <p>Hear from clients who trust Argus CRE. Our results-driven approach and personalized service speak volumes through the success of those we serve.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 px-0">
                    <a href="#">
                        <div class="homeAddSec2Col3">
                            <h2>Insights</h2>
                            <p>Explore market trends, investment strategies, and expert perspectives from the Argus CRE team—curated to keep you informed and ahead.</p>
                        </div>
                    </a>                  
                </div>
            </div>
        </div>
    </div>
</div>

<div class="homeAddSec3 pb-6">
    <div class="container-xl">
        <div class="row align-items-center">
            <div class="col-lg-6 col-12">
                <div class="homeAddSec3Left owl-carousel owl-theme">
                    <div class="homeAddSec3LeftThumb">
                        <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/homeAddSec3Left1.jpg" alt="">
                    </div>
                    <div class="homeAddSec3LeftThumb">
                        <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/homeAddSec3Left2.jpg" alt="">
                    </div>
                    <div class="homeAddSec3LeftThumb">
                        <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/homeAddSec3Left3.jpg" alt="">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-12 mt-lg-0 mt-4">
                <div class="homeAddSec3Right">
                    <h2>Our Southern <br> Reach.</h2>
                    <p>Strategic multi-family investments that strengthen communities and drive lasting value across the Southeast.</p>
                    <div class="homeAddSec3RightInner">
                        <h3>Investing in Communities, Building Futures</h3>
                        <p>At Argus CRE, a South Carolina-based firm, we specialize in multi-family real estate investments across North Carolina, Georgia, Tennessee, Alabama, and Mississippi—delivering high-quality, affordable housing solutions that strengthen neighborhoods and enhance lives. Through innovative design, sustainable practices, and strategic partnerships, we create lasting value for both investors and communities.</p>
                        <a href="<?php bloginfo('url')?>/about" class="cusBtn cusBtn3">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="homeAddSec3Bottom">
                    <div class="homeAddSec3BottomStats">
                        <h2><span>$</span><span class="counter">75</span><span>M</span></h2>
                        <p>Total Transactions</p>
                    </div>
                    <div class="homeAddSec3BottomStats">
                        <h2><span>$</span><span class="counter">42</span><span>M</span></h2>
                        <p>Total Assets</p>
                    </div>
                    <div class="homeAddSec3BottomStats">
                        <h2><span class="counter">30</span><span>+</span></h2>
                        <p>Years of Experience</p>
                    </div>
                    <div class="homeAddSec3BottomStats">
                        <h2><span class="counter">1.5</span><span>M</span></h2>
                        <p>Square Feet Managed</p>
                    </div>
                    <div class="homeAddSec3BottomStats">
                        <h2><span class="counter">27</span></h2>
                        <p>Team Members</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="homeAddSec4 d-flex align-items-center">
    <div class="container-xl">
        <div class="row">
            <div class="col-xl-4 col-md-6 col-12 ">
                <div class="homeAddSec4Col1 mb-md-0 mb-5">
                    <h4>Our Services.</h4>
                    <p>At ArgusCRE, our services are designed to deliver more than returns — we create impact. From strategic investment planning to commercial and residential real estate solutions, we help clients unlock opportunities that strengthen communities, maximize value, and drive long-term growth across the Southeast.</p>
                </div>
            </div>
            <?php
            $args = query_posts(
                array(
                    'post_type' => 'our_services', // This is the name of your CPT
                    'order' => 'ASC',
                    'posts_per_page' => -1
                )
            );
            if (have_posts()) {
                while (have_posts()) : the_post();
                    $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                    $citystate = get_field("address");
                    $shortText = wp_trim_words(get_the_excerpt(), 12);
            ?>

                    <div class="modal fade" id="exampleModal-<?php the_ID(); ?>" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h1 class="modal-title fs-5" id="exampleModalLabel"><?php the_title(); ?></h1>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <?php the_content(); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 col-12">
                        <div class="homeAddSec4Col">
                            <img src="<?php echo $image[0]; ?>" alt="">
                            <h4><?php the_title(); ?></h4>
                            <p><?php echo $shortText; ?></p>
                            <a href="#exampleModal-<?php the_ID(); ?>" data-bs-toggle="modal">
                                <i class="fa-solid fa-plus"></i>
                            </a>
                        </div>
                    </div>
            <?php
                endwhile;
            }
            wp_reset_query();
            ?>

                <div class="col-xl-4 col-md-6 col-12">
                    <div class="homeAddSec4Col">
                        <img src="<?php echo $image[0]; ?>" alt="">
                        <h4>Request <br> Consultation</h4>
                        <p><a href="<?php bloginfo('url')?>/contact">Contact Us</a></p>
                    </div>
                </div>
        </div>
    </div>
</div>

<div class="hAddSec4 py-6" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/featured-properties-bg.jpg') center center no-repeat; background-size: cover;">
    <div class="row">
        <div class="col-12">
            <div class="headerWraper pb-5 pt-7">
                <div class="hAddSec4Head">
                    <h2>Featured <br>Properties</h2>
                    <p>Explore a curated selection of commercial and multi-family properties across the Southeast. Each site is strategically<br> positioned to deliver long-term value and support community growth.</p>
                </div>
            </div>
            <div class="wrapper">
                <div id="quote-slider" class="splide">
                    <div class="splide__track">
                        <div class="splide__list">
                            <?php
                            $args = array(
                                'post_type'      => 'properties',
                                'posts_per_page' => -1, // Sabhi properties show karne ke liye
                            );
                            $properties_query = new WP_Query($args);

                            if ($properties_query->have_posts()) :
                                while ($properties_query->have_posts()) : $properties_query->the_post();
                            ?>
                                <div class="splide__slide">
                                    <div class="hAddSec4Col">
                                        <div class="count"><span><?php the_field('count');?></span></div>
                                        <div class="info">
                                            <h3><?php the_field('address'); ?>, <?php the_field('citystate'); ?></h3>
                                            <ul>
                                                <li><?php the_field('lot_size')?></li>
                                                <li><?php the_field('zoning')?></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            <?php
                                endwhile;
                                wp_reset_postdata();
                            else :
                                echo '<li class="splide__slide"><h2>No properties found.</h2></li>';
                            endif;
                            ?>
                        </div>
                    </div>
                </div>

                <div id="image-slider" class="splide">
                    <div class="splide__track">
                        <ul class="splide__list">
                            <?php
                            if ($properties_query->have_posts()) :
                                while ($properties_query->have_posts()) : $properties_query->the_post();
                                    if (has_post_thumbnail()) :
                            ?>
                                    <li class="splide__slide">
                                        <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" alt="<?php the_title(); ?>">
                                    </li>
                            <?php
                                    endif;
                                endwhile;
                                wp_reset_postdata();
                            else :
                                echo '<li class="splide__slide"><p>No images available.</p></li>';
                            endif;
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-xl">
        <div class="row pt-5">
            <div class="col-12">
                <a href="#" class="cusBtn cusBtn4">View All Properties</a>
            </div>
        </div>
    </div>
</div>

<div id="testimonial-sec" class="negitivePos"></div>
<div class="homeAddSec5 pt-6">
    <div class="container-xl">
        <div class="row pb-4">
            <div class="col-12">
                <div class="homeAddSec5Head">
                    <h2>What Our Clients Say</h2>
                    <p>Our clients trust us to deliver more than real estate solutions—they count on us for insight, integrity, and impact. Here’s what they have to say about working with ArgusCRE.</p>
                </div>
            </div>
        </div>
        <div class="row position-relative justify-content-end">
            <div class="col-lg-5 homeAddSec5Left" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/homeAddSec5Left.jpg') left center no-repeat; background-size: cover;"></div>
            <div class="col-lg-9">
                <div class="homeAddSec5RightSlide owl-carousel owl-theme">
                    <?php
                    $args = query_posts(
                        array(
                            'post_type' => 'testimonials', // This is the name of your CPT
                            'order' => 'ASC',
                            'posts_per_page' => -1
                        )
                    );
                    if (have_posts()) {
                        while (have_posts()) : the_post();
                            $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                            $citystate = get_field("address");
                            $shortText = wp_trim_words(get_the_excerpt(), 12);
                    ?>
                            <div class="homeAddSec5Right">
                                <h3><?php the_title(); ?></h3>
                                <?php the_content(); ?>
                                <div class="homeAddSec5RightLogo">
                                    <img src="<?php echo $image[0] ?>">
                                </div>
                            </div>
                    <?php
                        endwhile;
                    }
                    wp_reset_query();
                    ?>
                </div>
            </div>
            <div class="col-lg-7">
                <div class="homeAddSec5RightBottom">
                    <div class="top">
                        <h3>Happy Clients</h3>
                        <h3>100%</h3>
                    </div>
                    <div class="bottom">
                        <h3>Projects With Purpose</h3>
                        <h3>100%</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="homeAddSec6 py-6" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/homeAddSec6Bg.jpg') center center no-repeat; background-size:cover;">
    <div class="container-xl">
        <div class="row ">
            <div class="col-md-6 col-12">
                <div class="homeAddSec6Left">
                    <h2>Get in Touch</h2>
                    <p>Have a question? Fill out the contact form and we will be in touch as soon as possible.</p>
                </div>
            </div>
            <div class="col-md-6 col-12">
                <div class="homeAddSec6Right">
                    <?php echo do_shortcode('[contact-form-7 id="6378e04" title="Contact form 1"]'); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>


<script src="https://code.jquery.com/jquery-1.12.4.min.js" integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/2.0.3/waypoints.min.js"></script>
<script src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/js/counter.js"></script>
<script>
    jQuery(document).ready(function($) {
        $('.counter').counterUp({
            delay: 10, // the delay time in ms
            time: 3000 // the speed time in ms
        });
    });
</script>

<link href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.3/dist/css/splide.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.3/dist/js/splide.min.js"></script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
    var quoteSlider = new Splide("#quote-slider", {
        type: "loop",
        direction: "ttb", // Vertical direction
        height: "310px", // Adjust as per requirement
        perPage: 2.5,
        perMove: 1,
        pagination: false,
        arrows: false,
        autoplay: true,
        interval: 4000,
        pauseOnHover: false,
        pauseOnFocus: false,
        trimSpace: false,
        padding: { bottom: "4%" }, // Adjust padding for vertical layout
        breakpoints: {
            768: {
                perPage: 1.5,
                padding: { bottom: "15%" },
            },
            480: {
                perPage: 1,
                padding: { bottom: "0%" },
            },
        }
    });

    var imageSlider = new Splide("#image-slider", {
        type: "loop",
        perPage: 2.5,
        perMove: 1,
        focus: "left",
        pagination: false,
        arrows: false,
        autoplay: true,
        interval: 4000,
        pauseOnHover: false,
        pauseOnFocus: false,
        trimSpace: false,  
        padding: { right: "4%" },
        breakpoints: {
            768: {
                perPage: 1.5,
                padding: { right: "15%" },
            },
            480: {
                perPage: 1,
                padding: { right: "0%" },
            },
        },
    });

    // imageSlider.on("move", function(newIndex) {
    //     quoteSlider.go(newIndex);
    // });

imageSlider.sync( quoteSlider );
imageSlider.mount();
quoteSlider.mount();
 
});

</script>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const topSection = document.querySelector(".homeAddSec5RightBottom .top");
    const bottomSection = document.querySelector(".homeAddSec5RightBottom .bottom");
    const topCounter = topSection.querySelector("h3:last-child");
    const bottomCounter = bottomSection.querySelector("h3:last-child");

    function animateCounter(element, endValue, duration) {
        let start = 0;
        const increment = (endValue - start) / (duration / 20);

        const interval = setInterval(() => {
            start += increment;
            if (start >= endValue) {
                start = endValue;
                clearInterval(interval);
            }
            element.textContent = Math.floor(start) + "%";
        }, 20);
    }

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                if (entry.target === topSection) {
                    topSection.classList.add("active");
                    animateCounter(topCounter, 100, 2000);
                }

                if (entry.target === bottomSection) {
                    bottomSection.classList.add("active"); // Even though no :after transition, for consistency
                    animateCounter(bottomCounter, 100, 2000);
                }

                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.5
    });

    observer.observe(topSection);
    observer.observe(bottomSection);
});
</script>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
<script>
    $(document).ready(function() {
        $(".homeAddSec3Left").owlCarousel({
            loop: true,
            margin: 0,
            nav: false,
            dots: false,
            items: 1,
            autoplay: true,
            autoplayTimeout: 3000,
            navText: [
                '<span class="fa-solid fa-arrow-right-long left"></span>',
                '<span class="fa-solid fa-arrow-right-long"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                991: {
                    items: 1
                }
            },
        });
        $(".homeAddSec5RightSlide").owlCarousel({
            loop: true,
            margin: 20,
            nav: false,
            dots: false,
            items: 3,
            autoplayTimeout: 3000,
            autoplay: true,
            navText: [
                '<span class="fa-solid fa-arrow-right-long left"></span>',
                '<span class="fa-solid fa-arrow-right-long"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                991: {
                    items: 2
                }
            },
        });
    });
</script>