<?php
/**
 * Demo page to showcase the improved file upload field
 * This is just for demonstration purposes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include the dynamic fields file
require_once plugin_dir_path(__FILE__) . 'partials/metabox/fields/dynamic-fields.php';

// Create a mock post object for demonstration
$demo_post = new stdClass();
$demo_post->ID = 0; // Use 0 for demo

?>
<!DOCTYPE html>
<html>
<head>
    <title>FCRE File Upload Field Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            margin: 40px;
            background: #f1f1f1;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.13);
        }
        .demo-title {
            color: #23282d;
            margin-bottom: 30px;
            border-bottom: 1px solid #e1e1e1;
            padding-bottom: 15px;
        }
        .demo-section {
            margin-bottom: 40px;
        }
        .demo-section h3 {
            color: #555;
            margin-bottom: 15px;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        .demo-note {
            background: #e7f3ff;
            border-left: 4px solid #0073aa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .demo-note h4 {
            margin-top: 0;
            color: #0073aa;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:before {
            content: "✓";
            color: #00a32a;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
    
    <!-- WordPress Admin Styles -->
    <link rel="stylesheet" href="<?php echo admin_url('css/dashicons.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo admin_url('css/buttons.min.css'); ?>">
    
    <!-- Plugin Styles -->
    <link rel="stylesheet" href="<?php echo plugin_dir_url(__FILE__) . 'css/fcre-properties-admin.css'; ?>">
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">FCRE File Upload Field - Improved UI Demo</h1>
        
        <div class="demo-note">
            <h4>🎉 New Features</h4>
            <ul class="feature-list">
                <li>ACF-style file preview with icons</li>
                <li>File name and size display</li>
                <li>Drag and drop support</li>
                <li>Better visual feedback</li>
                <li>Responsive design</li>
                <li>Loading and success states</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>File Upload Examples</h3>
            
            <div class="demo-grid">
                <div>
                    <h4>Property Flyer Upload</h4>
                    <?php
                    fcre_file_upload_field($demo_post, [
                        'meta_key'    => 'demo_flyer',
                        'field_name'  => 'demo_flyer',
                        'label'       => 'Upload Property Flyer',
                        'placeholder' => 'Select or drag a flyer file',
                        'group_class' => 'demo-field',
                    ]);
                    ?>
                </div>
                
                <div>
                    <h4>Offering Memorandum Upload</h4>
                    <?php
                    fcre_file_upload_field($demo_post, [
                        'meta_key'    => 'demo_om',
                        'field_name'  => 'demo_om',
                        'label'       => 'Upload Offering Memorandum',
                        'placeholder' => 'Select or drag an OM file',
                        'group_class' => 'demo-field',
                    ]);
                    ?>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>Supported File Types</h3>
            <p>The field automatically detects file types and shows appropriate icons for:</p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-top: 15px;">
                <div>📄 PDF files</div>
                <div>📝 Word documents</div>
                <div>📊 Excel spreadsheets</div>
                <div>📋 PowerPoint presentations</div>
                <div>🗜️ ZIP archives</div>
                <div>🖼️ Image files</div>
            </div>
        </div>

        <div class="demo-note">
            <h4>💡 How to Use</h4>
            <p>1. Click "Select File" button or drag and drop a file onto the upload area<br>
            2. Choose a file from the WordPress Media Library<br>
            3. The file will be displayed with its icon, name, and size<br>
            4. Use the edit button to change the file or trash button to remove it</p>
        </div>
    </div>

    <!-- WordPress Media Scripts -->
    <script src="<?php echo includes_url('js/jquery/jquery.min.js'); ?>"></script>
    <script>
        // Mock WordPress media object for demo
        if (typeof wp === 'undefined') {
            window.wp = {
                media: function(options) {
                    return {
                        on: function(event, callback) {
                            // Mock implementation
                            if (event === 'select') {
                                setTimeout(() => {
                                    callback();
                                }, 1000);
                            }
                        },
                        open: function() {
                            alert('This is a demo. In the real WordPress admin, this would open the Media Library.');
                        },
                        state: function() {
                            return {
                                get: function() {
                                    return {
                                        first: function() {
                                            return {
                                                toJSON: function() {
                                                    return {
                                                        id: 123,
                                                        url: 'https://example.com/demo-file.pdf',
                                                        filename: 'demo-file.pdf',
                                                        title: 'Demo File',
                                                        filesizeHumanReadable: '2.5 MB'
                                                    };
                                                }
                                            };
                                        }
                                    };
                                }
                            };
                        }
                    };
                }
            };
        }
    </script>
    
    <!-- Plugin Scripts -->
    <script src="<?php echo plugin_dir_url(__FILE__) . 'js/fcre-properties-admin.js'; ?>"></script>
</body>
</html>
