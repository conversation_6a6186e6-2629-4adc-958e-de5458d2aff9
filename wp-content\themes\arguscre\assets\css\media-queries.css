@media (max-width: 1200px) {
    .homeAddSec3Bottom {justify-content: center;}
    .wrapper>div#quote-slider {width: 35%;}
    .wrapper>div#image-slider {width: 65%;}
    .aboutAddSec1 {position: relative; z-index: 0;}
    .aboutIntroSliderBox {position: absolute; top: 0; right: 0; width: 100%; height: 100%; z-index: -1; padding: 0; margin: 0;}
    .aboutIntroSliderBox::before {content:''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; 
        background: rgba(255, 255, 255, 0.9); z-index: 3;
    }
    .aboutAddSec1Bottom {justify-content: center;}
}

@media (max-width: 991px) {
    .homeAddSec3Left .owl-carousel .owl-item img {width: 100%; height: auto; object-fit: cover; object-position: center;
    aspect-ratio: 16/9;}
    .homeAddSec3Right h2 {font-size: 28px; line-height: 1.1;}
    .homeAddSec3Right h2 br {display: none;}
    .homeAddSec3RightInner {margin-left: 0;}
    .homeAddSec5Left {z-index: 0;}
    .homeAddSec5Left:before {content:''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; 
        background: rgba(255, 255, 255, 0.85); z-index: -1;
    }
}

@media (max-width: 767px) {
    .homeAddSec1 .homeAddSec1InnerText h1 {font-size: 32px;}
    .homeAddSec1 .homeAddSec1InnerText p {font-size: 16px;}
    .wrapper>div#quote-slider {width: 45%;}
    .wrapper>div#image-slider {width: 55%;}
    .pSingleLeftSec h1 {font-size: 22px; line-height: 1.1;}
    .cAddSec1 .cAddSec1LeftCol h2 {font-size: 34px; line-height: 1.1;}
}

@media (max-width: 576px) {
    .homeAddSec1 .homeAddSec1InnerText h1 {font-size: 20px;}
    .homeAddSec1 .homeAddSec1InnerText p {font-size: 14px;}
    .homeAddSec1 .homeAddSec1InnerText p br {display: none;}
    .cusBtn {font-size: 14px; padding: 7px 15px;}
    .hAddSec4 .wrapper {flex-wrap: wrap;}
    .wrapper>div#quote-slider {width: 100%; height: 220px;}
    .wrapper>div#image-slider {width: 100%;}
    .wrapper>div#quote-slider {padding-right: 20px;}
}

@media (max-width: 480px) {
    .wrapper>div#quote-slider {height: 120px;}
}