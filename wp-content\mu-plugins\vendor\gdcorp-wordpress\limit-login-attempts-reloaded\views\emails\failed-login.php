<?php
use LLAR\Core\Helpers;

if ( ! defined( 'ABSPATH' ) ) {
	exit();
}
?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" style="font-family:arial, 'helvetica neue', helvetica, sans-serif">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html charset=UTF-8" />
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="telephone=no" name="format-detection">
    <style type="text/css">
        a {
            text-decoration: none;
        }
    </style>
    <![endif]--><!--[if gte mso 9]>
    <style>sup {
        font-size: 100% !important;
    }</style><![endif]--><!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG></o:AllowPNG>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
    <style type="text/css">
        #outlook a {
            padding: 0;
        }

        .es-button {
            mso-style-priority: 100 !important;
            text-decoration: none !important;
        }

        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }

        .es-button-border:hover a.es-button, .es-button-border:hover button.es-button {
            background: #56d66b !important;
        }

        .es-button-border:hover {
            border-color: #42d159 #42d159 #42d159 #42d159 !important;
            background: #56d66b !important;
        }

        td .es-button-border:hover a.es-button-1 {
            background: #50c1cd !important;
        }

        td .es-button-border-2:hover {
            background: #50c1cd !important;
            border-color: #33a9b6 #33a9b6 #5bbec7 #33a9b6 !important;
        }

        @media only screen and (max-width: 600px) {
            p, ul li, ol li, a {
                line-height: 150% !important
            }

            h1, h2, h3, h1 a, h2 a, h3 a {
                line-height: 120%
            }

            h1 {
                font-size: 30px !important;
                text-align: left
            }

            h2 {
                font-size: 24px !important;
                text-align: left
            }

            h3 {
                font-size: 20px !important;
                text-align: left
            }

            .es-header-body h1 a, .es-content-body h1 a, .es-footer-body h1 a {
                font-size: 30px !important;
                text-align: left
            }

            .es-header-body h2 a, .es-content-body h2 a, .es-footer-body h2 a {
                font-size: 24px !important;
                text-align: left
            }

            .es-header-body h3 a, .es-content-body h3 a, .es-footer-body h3 a {
                font-size: 20px !important;
                text-align: left
            }

            .es-menu td a {
                font-size: 14px !important
            }

            .es-header-body p, .es-header-body ul li, .es-header-body ol li, .es-header-body a {
                font-size: 14px !important
            }

            .es-content-body p, .es-content-body ul li, .es-content-body ol li, .es-content-body a {
                font-size: 14px !important
            }

            .es-footer-body p, .es-footer-body ul li, .es-footer-body ol li, .es-footer-body a {
                font-size: 14px !important
            }

            .es-infoblock p, .es-infoblock ul li, .es-infoblock ol li, .es-infoblock a {
                font-size: 12px !important
            }

            *[class="gmail-fix"] {
                display: none !important
            }

            .es-m-txt-c, .es-m-txt-c h1, .es-m-txt-c h2, .es-m-txt-c h3 {
                text-align: center !important
            }

            .es-m-txt-r, .es-m-txt-r h1, .es-m-txt-r h2, .es-m-txt-r h3 {
                text-align: right !important
            }

            .es-m-txt-l, .es-m-txt-l h1, .es-m-txt-l h2, .es-m-txt-l h3 {
                text-align: left !important
            }

            .es-m-txt-r img, .es-m-txt-c img, .es-m-txt-l img {
                display: inline !important
            }

            .es-button-border {
                display: inline-block !important
            }

            a.es-button, button.es-button {
                font-size: 18px !important;
                display: inline-block !important
            }

            .es-adaptive table {
                width: 100% !important
            }

            .es-content table, .es-header table, .es-footer table, .es-content, .es-header {
                width: 100% !important;
                max-width: 600px !important
            }

            .es-menu td {
                width: 1% !important
            }

            table.es-table-not-adapt, .esd-block-html table {
                width: auto !important
            }
        }
    </style>
</head>
<body style="width:100%;font-family:arial, 'helvetica neue', helvetica, sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0">
<div class="es-wrapper-color" style="background-color:#F6F6F6"><!--[if gte mso 9]>
    <v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">
        <v:fill type="tile" color="#f6f6f6"></v:fill>
    </v:background>
    <![endif]-->
    <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0"
           style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;background-color:#F6F6F6">
        <tr>
            <td valign="top" style="padding:0;Margin:0">
                <table class="es-header" cellspacing="0" cellpadding="0" align="center"
                       style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%;background-color:transparent;background-repeat:repeat;background-position:center top">
                    <tr>
                        <td align="center" style="padding:0;Margin:0">
                            <table class="es-header-body"
                                   style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;width:600px"
                                   cellspacing="0" cellpadding="0" align="center">
                                <tr>
                                    <td align="left"
                                        style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px">
                                        <table width="100%" cellspacing="0" cellpadding="0"
                                               style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">
                                            <tr>
                                                <td valign="top" align="center" style="padding:0;Margin:0;width:560px">
                                                    <table width="100%" cellspacing="0" cellpadding="0"
                                                           role="presentation"
                                                           style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">
                                                        <tr>
                                                            <td style="padding:0;Margin:0;font-size:0px" align="left">
                                                                <img src="cid:logo" width="220" alt="">
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td height="25" align="center"
                                                                style="padding:0;Margin:0"></td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <table class="es-content" cellspacing="0" cellpadding="0" align="center"
                       style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;table-layout:fixed !important;width:100%">
                    <tr>
                        <td align="center" style="padding:0;Margin:0">
                            <table class="es-content-body" cellspacing="0" cellpadding="0" bgcolor="#ffffff"
                                   align="center"
                                   style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:#FFFFFF;width:600px">
                                <tr>
                                    <td align="left"
                                        style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px">
                                        <table width="100%" cellspacing="0" cellpadding="0"
                                               style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">
                                            <tr>
                                                <td valign="top" align="center" style="padding:0;Margin:0;width:560px">
                                                    <table width="100%" cellspacing="0" cellpadding="0"
                                                           role="presentation"
                                                           style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">
                                                        <tr>
                                                            <td style="padding:0;Margin:0">
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px"><?php ( !empty( $admin_name ) )
                                                                        ? _e( 'Hello {name},', 'limit-login-attempts-reloaded' )
                                                                        : _e( 'Hello,', 'limit-login-attempts-reloaded' ); ?></p><br>
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <?php _e( 'This notification was sent automatically via Limit Login Attempts Reloaded Plugin.', 'limit-login-attempts-reloaded' ); ?></p>
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <?php _e( 'This is installed on your <b>{domain}</b> WordPress site.', 'limit-login-attempts-reloaded' ); ?></p><br>
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <?php _e( 'The failed login details include:', 'limit-login-attempts-reloaded' ); ?></p>
                                                                <ul>
                                                                    <li style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;Margin-bottom:15px;margin-left:0;color:#333333;font-size:14px">
                                                                        <?php _e( '{attempts_count} failed login attempts ({lockouts_count} lockout(s)) from IP <b>{ip_address}</b>', 'limit-login-attempts-reloaded' ); ?>
                                                                    </li>
                                                                    <li style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;Margin-bottom:15px;margin-left:0;color:#333333;font-size:14px">
                                                                        <?php _e( 'Last user attempted: <b>{username}</b>', 'limit-login-attempts-reloaded' ); ?></li>
                                                                    <li style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;Margin-bottom:15px;margin-left:0;color:#333333;font-size:14px">
                                                                        <?php _e( 'IP was blocked for {blocked_duration}', 'limit-login-attempts-reloaded' ); ?>
                                                                    </li>
                                                                </ul>
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <?php _e( 'Please visit your WordPress dashboard for additional details, investigation options, and help articles.', 'limit-login-attempts-reloaded' ); ?></p></td>
                                                        </tr>
                                                        <tr>
                                                            <td align="center" style="padding:20px;Margin:0">
                                                                <!--[if mso]><a href="{dashboard_url}" target="_blank"
                                                                                hidden>
                                                                    <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml"
                                                                                 xmlns:w="urn:schemas-microsoft-com:office:word"
                                                                                 esdevVmlButton href="{dashboard_url}"
                                                                                 style="height:40px; v-text-anchor:middle; width:180px"
                                                                                 arcsize="50%" strokecolor="#3da7b1"
                                                                                 strokeweight="1px" fillcolor="#50c1cd">
                                                                        <w:anchorlock></w:anchorlock>
                                                                        <center style='color:#ffffff; font-family:arial, "helvetica neue", helvetica, sans-serif; font-size:14px; font-weight:400; line-height:14px;  mso-text-raise:1px'>
                                                                            <?php _e( 'Go to Dashboard', 'limit-login-attempts-reloaded' ); ?>
                                                                        </center>
                                                                    </v:roundrect>
                                                                </a>
                                                                <![endif]--><!--[if !mso]><!-- --><span
                                                                        class="msohide es-button-border-2 es-button-border"
                                                                        style="border-style:solid;border-color:#50c1cd #50c1cd #3da7b1;background:#50c1cd;border-width:0px 0px 2px 0px;display:inline-block;border-radius:30px;width:auto;mso-border-alt:10px;mso-hide:all"><a
                                                                            href="{dashboard_url}"
                                                                            class="es-button es-button-1"
                                                                            target="_blank"
                                                                            style="mso-style-priority:100 !important;text-decoration:none;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;color:#FFFFFF;font-size:18px;display:inline-block;background:#50c1cd;border-radius:30px;font-family:arial, 'helvetica neue', helvetica, sans-serif;font-weight:normal;font-style:normal;line-height:22px;width:auto;text-align:center;padding:10px 20px 10px 20px;border-color:#50c1cd">Go to Dashboard</a></span>
                                                                <!--<![endif]--></td>
                                                        </tr>
                                                        <tr>
                                                            <td style="padding:0;Margin:0"><p
                                                                        style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <?php _e( '<b>Experiencing frequent attacks or degraded performance?</b> You can now receive premium protection for FREE with ', 'limit-login-attempts-reloaded' ); ?><a href="{premium_url}"
                                                                                             target="_blank"
                                                                                             style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;text-decoration:underline;color:#FDA33B;font-size:14px">
                                                                        <?php _e( 'Micro Cloud©.', 'limit-login-attempts-reloaded' ); ?></a>
                                                                        <?php _e( ' Go to your LLAR dashboard to get starte.', 'limit-login-attempts-reloaded' ); ?></p></td>
                                                        </tr>
                                                        <tr>
                                                            <td height="25" align="center"
                                                                style="padding:0;Margin:0"></td>
                                                        </tr>
                                                        <tr>
                                                            <td style="padding:0;Margin:0"><h3
                                                                        style="Margin:0;line-height:24px;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;font-size:20px;font-style:normal;font-weight:normal;color:#333333">
                                                                    <?php _e( 'Frequently Asked Questions', 'limit-login-attempts-reloaded' ); ?></h3><br>
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <b><?php _e( 'What is a failed login attempt?', 'limit-login-attempts-reloaded' ); ?></b><br>
                                                                    <?php _e( 'A failed login attempt is when an IP address uses incorrect credentials to log into your website. The IP address could be a human operator, or a program designed to guess your password.', 'limit-login-attempts-reloaded' ); ?></p><br>
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <b><?php _e( 'Why am I getting these emails?', 'limit-login-attempts-reloaded' ); ?></b><br>
                                                                    <?php _e( 'You are receiving this email because there was a failed login attempt on your website {domain}. If you\'d like to opt out of these notifications, please click the “Unsubscribe” link below.', 'limit-login-attempts-reloaded' ); ?></p><br>
                                                                <p style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <b><?php _e( 'How dangerous is this failed login attempt?', 'limit-login-attempts-reloaded' ); ?></b><br><?php
                                                                    _e( 'Unfortunately, we cannot determine the severity of the IP address with the free version of the plugin. If the IP continues to make attempts and is not recognized by your organization, then it\'s likely to have malicious intent. Depending on how frequent the attacks are, you may experience performance issues. In the plugin dashboard, you can investigate the frequency of the failed login attempts in the logs and take additional steps to protect your website (i.e. adding them to the block list). You can visit the ', 'limit-login-attempts-reloaded' ); ?><a
                                                                            href="{llar_url}" target="_blank"
                                                                            style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;text-decoration:underline;color:#FDA33B;font-size:14px">
                                                                        <?php _e( 'Limit Login Attempts Reloaded website', 'limit-login-attempts-reloaded' ); ?></a><?php _e( ' for more information on our premium services, which can automatically block and detect malicious IP addresses.', 'limit-login-attempts-reloaded' ); ?></p></td>
                                                        </tr>
                                                        <tr>
                                                            <td height="25" align="center"
                                                                style="padding:0;Margin:0"></td>
                                                        </tr>
                                                        <?php if( Helpers::is_mu() ) : ?>
                                                        <tr>
                                                            <td style="padding:0;Margin:0"><p
                                                                        style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <i><?php _e( 'This alert was sent by your website where Limit Login Attempts Reloaded free version is installed and you are listed as the admin. If you are a GoDaddy customer, the plugin is installed into a must-use (MU) folder.', 'limit-login-attempts-reloaded' ); ?></i></p>
                                                            </td>
                                                        </tr>
                                                        <?php endif; ?>
                                                        <tr>
                                                            <td style="padding:0;Margin:0;padding-top:20px;padding-bottom:20px;font-size:0"
                                                                align="center">
                                                                <table width="100%" height="100%" cellspacing="0"
                                                                       cellpadding="0" border="0" role="presentation"
                                                                       style="mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px">
                                                                    <tr>
                                                                        <td style="padding:0;Margin:0;border-bottom:1px solid #cccccc;background:unset;height:1px;width:100%;margin:0px"></td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="padding:0;Margin:0"><p
                                                                        style="Margin:0;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;font-family:arial, 'helvetica neue', helvetica, sans-serif;line-height:21px;color:#333333;font-size:14px">
                                                                    <a href="{unsubscribe_url}" target="_blank"
                                                                       style="-webkit-text-size-adjust:none;-ms-text-size-adjust:none;mso-line-height-rule:exactly;text-decoration:underline;color:#FDA33B;font-size:14px"><?php _e( 'Unsubscribe', 'limit-login-attempts-reloaded' ); ?></a>
                                                                    <?php _e( 'from these notifications.', 'limit-login-attempts-reloaded' ); ?></p></td>
                                                        </tr>
                                                        <tr>
                                                            <td height="15" align="center"
                                                                style="padding:0;Margin:0"></td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="25" align="center"
                            style="padding:0;Margin:0"></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
</body>
</html>