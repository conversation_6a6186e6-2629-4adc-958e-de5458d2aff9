@charset "UTF-8";
/*
Theme Name: Argus CRE Wordpress Theme
Author: <PERSON>
Author URI: https://focusedcre.com
Description: Custom wordpress theme for Argus CRE
Version: 1
*/


.fcre-single-property{
	margin-top: 130px;
}
.fcre-single-property .fcre-tab-content{
    min-height: 64vh;
}
:root{
	--primary: #6ea9dc;
}
html, body {
    overflow-x: hidden;
    width: 100%;
}

body{font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: normal;}
#mainWebContainer {
    overflow-x: hidden;
    position: relative;
}
/************************* fonts, buttons, icons and text blocks styles**********************************/
h1{font-size: 50px; color: #000000; line-height: 50px; }
h2{font-size: 44px; color: #000000; line-height: 44px; }
h3{font-size: 24px; color: #000000; line-height: 26px; }
h4{font-size: 20px; color: #000000; line-height: 30px; }
h5{font-size: 16px; color: #000000; line-height: 24px; }
h6{font-size: 14px; color: #000000; line-height: 18px; }
.heading-inline{display: inline !important;}
a{color: #000000; font-weight: 400;text-decoration: none; -webkit-transition: 0.3s ease-in-out !important;-moz-transition: 0.3s ease-in-out !important;-ms-transition: 0.3s ease-in-out !important;-o-transition: 0.3s ease-in-out !important;transition: 0.3s ease-in-out !important;}
a:hover{color: #000000; text-decoration: none;}
a:focus{text-decoration: none; outline: none}
ul{margin: 0; padding: 0}
ul li{list-style: none;}
img{image-rendering: -webkit-optimize-contrast;}

#map{height: 400px;}

/*--------------------------------------------------------------
# Top Bar
--------------------------------------------------------------*/
#topbar{background:#306178; height:40px; font-size:14px; border-bottom:1px solid #b49e3f; transition:all 0.5s;}
#topbar.topbar-scrolled{top:-40px;}
#topbar .contact-info a{color:#ffffff; font-size:14px; line-height:14px; font-weight:500;}
#topbar .contact-info a:hover{color:#ffffff;}
#topbar .contact-info a i{color:#ffffff;}



.cusBtn{
	position: relative;
    padding: 10px 30px;
    font-weight: 700;
    display: inline-block;
    text-transform: uppercase;
}
.cusBtn1{
	border: 1px solid var(--primary);
	background: var(--primary);
	color: #ffffff;
}
.cusBtn1:hover{
	border: 1px solid #ffffff;
	background: transparent;
	color: #ffffff;
}


.cusBtn2{
	border: 1px solid #ffffff;
	background: transparent;
	color: #ffffff;
}
.cusBtn2:hover{
	border: 1px solid  var(--primary);
	background: var(--primary);
	color: #ffffff;
}


.cusBtn3{
	border: 1px solid var(--primary);
	background: var(--primary);
	color: #ffffff;
}
.cusBtn3:hover{
	border: 1px solid  var(--primary);
	background: transparent;
	color:  var(--primary);
}


.cusBtn4{
	border: 1px solid #000000;
	background: #000000;
	color: #ffffff;
    display: block;
    text-align: center;
}
.cusBtn4:hover{
	border: 1px solid  #ffffff;
	background: transparent;
	color:  #ffffff;
}







/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
#header {
	border-bottom: 1px solid #ffffff;
}
#header.header-scrolled {
	background: #000000;
	top: 0;
}
#header .logo img {width: 250px;}

.contactNo{font-size: 18px; font-weight: 500; color: #ffffff;}
.contactNo:hover{color: #ffffff;}
.contactNo i{font-size: 15px;}



/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/**
* Desktop Navigation
*/

/*--------------------------------------------------------------
# Desktop Navigation
--------------------------------------------------------------*/
@media (min-width:1024px){
	.navbar{padding:0;}
	.navbar ul{margin:0; padding:0; display:flex; list-style:none; align-items:center;}
	.navbar li{position:relative; padding: 0 15px 0 15px;}
    .navbar li:last-child{padding-right: 30px;}
	.navbar li a,
	.navbar li a:focus {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #ffffff;
		white-space: nowrap;
		transition: 0.3s;
		text-transform: uppercase;
		padding: 40px 0px 40px 0px;
		font-weight: 700;
	}	
	.navbar li a i,.navbar li a:focus i{font-size:12px; line-height:0; margin-left:5px;}
	.navbar li a:hover,
	.navbar li.current-menu-item a,
	.navbar li.current-menu-item:focus a,
	.navbar li:hover a{color:#ffffff;}
    .navbar li a:after {
        position: absolute;
        content: '';
        width: 0;
        height: 5px;
        bottom: -3px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--primary);
        -webkit-transition: width 0.3s ease;
        -moz-transition: width 0.3s ease;
        -ms-transition: width 0.3s ease;
        -o-transition: width 0.3s ease;
        transition: width 0.3s ease;
	}
	.navbar li.current-menu-item a:after, .navbar li a:hover:after {width: 100%;}
	.navbar li.menu-item-has-children a:after{
		content: "\f078";
		display: inline-block;
		font-family: 'fontawesome';
		position: absolute;
		top: 50%;
		right: 0;
		transform: translateY(-50%);
		font-size: 12px;
	}

	.navbar li.menu-item-has-children ul.sub-menu {
		display: block;
		position: absolute;
		left: 28px;
		top: calc(100% + 30px);
		margin: 0;
		padding: 10px 0;
		z-index: 99;
		opacity: 0;
		visibility: hidden;
		background: #fff;
		box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
		transition: 0.3s;
		border-radius: 4px;
	}
	.navbar li.menu-item-has-children ul.sub-menu li{min-width:200px;}
	.navbar li.menu-item-has-children ul.sub-menu a {
		padding: 10px 20px;
		font-size: 15px;
		text-transform: none;
		font-weight: 600;
		color: #082744;
	}	
	.navbar li.menu-item-has-children ul.sub-menu a i{font-size:12px;}
	.navbar li.menu-item-has-children ul.sub-menu a:hover,
	.navbar li.menu-item-has-children ul.sub-menu li:hover>a{color:#000000;}

}
@media (min-width:1024px) and (max-width:1366px){
	.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{left:-90%;}
	.navbar .menu-item-has-children .menu-item-has-children:hover>ul.sub-menu{left:-100%;}
}
@media (min-width:1024px) {.mobile-nav-show, .mobile-nav-hide {display: none;}}

/*--------------------------------------------------------------
# Mobile Navigation
--------------------------------------------------------------*/
@media (max-width:1023px){
    #header {
        padding: 15px 0;
    }
    #header .logo img {width: 200px;}
	.navbar{position:fixed; top:0; right:-100%; width:100%; max-width:400px; bottom:0; transition:0.3s; z-index:9997;}
	.navbar ul {
		position: absolute;
		inset: 0;
		padding: 50px 0 10px 0;
		margin: 0;
		background: #000;
		opacity: .95;
		overflow-y: auto;
		transition: 0.3s;
		z-index: 9998;
	}	
	.navbar a,.navbar a:focus{display:flex; align-items:center; justify-content:space-between; padding:10px 20px; font-size:15px; font-weight:600; color:rgba(255,255,255,0.7); white-space:nowrap; transition:0.3s;}
	.navbar a i,.navbar a:focus i{font-size:12px; line-height:0; margin-left:5px;}
	.navbar a:hover,.navbar .current-menu-item a,.navbar .current-menu-item:focus a,.navbar li:hover>a{color:#fff;}
	.navbar .menu-item-has-children ul.sub-menu,.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{position:static; display:none; padding:10px 0; margin:10px 20px; background-color:rgba(20,35,51,0.6);}
	.navbar .menu-item-has-children>.submenu-active,.navbar .menu-item-has-children .menu-item-has-children>.submenu-active{display:block;}
	.mobile-nav-show{
		font-size: 20px;
		cursor: pointer;
		line-height: 0;
		transition: 0.5s;
		color: #fff;
		padding-right: 30px;
	}
	.mobile-nav-hide {
		color: rgba(255, 255, 255, 0.9);
		font-size: 32px;
		cursor: pointer;
		line-height: 0;
		transition: 0.5s;
		position: fixed;
		right: 15px;
		top: 15px;
		z-index: 9999;
	}
	.mobile-nav-active{overflow:hidden;}
	.mobile-nav-active .navbar{right:0;}
	.mobile-nav-active .navbar:before {
		content: "";
		position: fixed;
		inset: 0;
		background: #000000;
		opacity: .7;
		z-index: 9996;
	}
	}
@media (min-width:1023px){.mobile-nav-show,.mobile-nav-hide{display:none !important;}}
.sub-menu-toggle{display: none !important;}




/*--------------------------------------------------------------
# Inner Main Banner
--------------------------------------------------------------*/



/*--------------------------------------------------------------
# Inner Main Banner
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Home Additional Section 1
--------------------------------------------------------------*/
.homeAddSec1{position:relative;}
.homeAddSec1:after{content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,.4); display:block;}
.homeAddSec1Inner{height:calc(100vh - 10px); min-height:575px; overflow:hidden; position:relative;}
.homeAddSec1Inner video{position:absolute; top:0; left:0; width:100%; height:100%; z-index:-1; object-fit:cover;}
.homeAddSec1 .homeAddSec1InnerText{top:50%; left:50%; transform:translate(-50%,-50%); 
    width:1140px; max-width:100%; z-index:1; position:absolute;
}
.homeAddSec1 .homeAddSec1InnerText h1{color:#ffffff; font-weight:900; font-size:40px; line-height:1;}
.homeAddSec1 .homeAddSec1InnerText p{color:#ffffff; font-size:20px; line-height:1.25;}
.homeAddSec1 a{margin-top:30px;}


/*--------------------------------------------------------------
# Home Additional Section 1
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home Additional Section 2
--------------------------------------------------------------*/

.homeAddSec2{position:relative;}
.homeAddSec2 .row {margin-top: -75px;}
.homeAddSec2Col1{text-align:center; padding:50px 20px; background:#000000; position: relative; transition: .3s;}
.homeAddSec2 a {display: block; height: 100%;}
.homeAddSec2 a > div {height: 100%;}
.homeAddSec2Col1:hover{transform: translateY(-25px);}
.homeAddSec2Col1 h2{font-size:20px; line-height:32px; text-transform:uppercase; font-weight:700; color:#ffffff;}
.homeAddSec2Col1 p{font-size:14px; line-height:22px; color:#ffffff; margin:0;}
.homeAddSec2Col2{text-align:center; padding:50px 20px; background:var(--primary); position: relative; transition: .3s;}
.homeAddSec2Col2:hover{transform: translateY(-25px);}
.homeAddSec2Col2 h2{font-size:20px; line-height:32px; text-transform:uppercase; font-weight:700; color:#ffffff;}
.homeAddSec2Col2 p{font-size:14px; line-height:22px; color:#ffffff; margin:0; min-height: 88px;}
.homeAddSec2Col3{text-align:center; padding:50px 20px; background:#d1d3d4; position: relative; transition: .3s;}
.homeAddSec2Col3:hover{transform: translateY(-25px);}
.homeAddSec2Col3 h2{font-size:20px; line-height:32px; text-transform:uppercase; font-weight:700;}
.homeAddSec2Col3 p{font-size:14px; line-height:22px; margin:0;min-height: 88px;}



/*--------------------------------------------------------------
# Home Additional Section 2
--------------------------------------------------------------*/

.negitivePos{position: relative; top:-100px}

/*--------------------------------------------------------------
# Home Additional Section 3
--------------------------------------------------------------*/
.homeAddSec3 .homeAddSec3Left img{width:100%;}
.homeAddSec3Right h2{font-weight:900; font-size:40px; line-height:40px; color:var(--primary);}
.homeAddSec3Right p{font-size:18px; line-height:22px;}
.homeAddSec3RightInner{background:#ffffff; margin-left:-150px; padding:40px; margin-top:30px; border:1px solid #eeeeee; box-shadow:5px 10px 10px 0 rgba(0,0,0,.3); position:relative; z-index:1;}
.homeAddSec3RightInner h3{font-weight:700; font-size:20px; line-height:30px; color:var(--primary);}
.homeAddSec3RightInner p{font-size:18px; line-height:22px; text-align:justify;}
.homeAddSec3Bottom{display:flex; flex-wrap:wrap; gap:50px; margin-top:50px; justify-content:space-between;}
.homeAddSec3BottomStats{text-align:center;}
.homeAddSec3BottomStats h2{margin:0; color:var(--primary); font-weight:800; font-size:40px; line-height:40px; text-transform:uppercase;}
.homeAddSec3BottomStats p{font-size:18px; font-family:"Cinzel",serif; font-weight:800;}



/*--------------------------------------------------------------
# Home Additional Section 3
--------------------------------------------------------------*/



/*--------------------------------------------------------------
# Home Additional Section 4
--------------------------------------------------------------*/
.homeAddSec4{background:#000000; padding:5rem 0 1rem 0 !important;}
.homeAddSec4Col1 h4{margin:0; font-size:26px; line-height:26px; font-weight:800; color:var(--primary);}
.homeAddSec4Col1 p{color:#ffffff; font-size:14px; margin:30px 0 0 0;}
.homeAddSec4Col{border:2px solid #ffffff; border-top:0; padding:60px 30px 30px 30px; margin-bottom:50px; position:relative; width:96%; max-width:100%;}
.homeAddSec4Col:before{content:''; position:absolute; top:0; left:0; width:30px; height:2px; background:#ffffff;}
.homeAddSec4Col:after{content:''; position:absolute; top:0; right:0; width:240px; height:2px; background:#ffffff;}
.homeAddSec4Col img{width:70px; position:absolute; top:-35px; filter:brightness(0) invert(1);}
.homeAddSec4Col h4{color:#ffffff; font-size:16px; line-height:22px; font-weight:700; margin-bottom:30px; text-transform:uppercase;}
.homeAddSec4Col p{color:#ffffff; font-size:14px; line-height:18px; min-height:52px; margin-bottom:0;}
.homeAddSec4Col > p > a{position: relative;}
.homeAddSec4Col > a{position:absolute; top:50%; right:-40px; transform:translate(-50%,-50%) scale(0); width:40px; height:40px; line-height:40px; text-align:center; border-radius:50%; display:flex; background:#ffffff; color:var(--primary); transition:.3s; justify-content:center; align-items:center;}
.homeAddSec4Col:hover > a{transform:translate(-50%,-50%) scale(1) rotate(360deg);}
.homeAddSec4Col:hover{border:2px solid var(--primary); border-top:0;}
.homeAddSec4Col:hover img{filter:none}
.homeAddSec4Col:hover:before{background:var(--primary);}
.homeAddSec4Col:hover:after{background:var(--primary);}
.homeAddSec4 .modal-content{padding:10px 20px;}
.homeAddSec4 .modal-content .modal-header h1 br{display:none;}
.homeAddSec4 .modal-content .modal-header h1{font-weight:700; color:var(--primary);}
.homeAddSec4 .modal-content .modal-body p{font-size:14px; line-height:20px; margin-bottom:0;}


/*--------------------------------------------------------------
# Home Additional Section 4
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home Additional Section 5
--------------------------------------------------------------*/



.homeAddSec5 .homeAddSec5Head{text-align:center; padding:0 80px;}
.homeAddSec5 .homeAddSec5Head h2{margin:0 0 30px 0; font-size:26px; line-height:26px; font-weight:800; color:var(--primary);}
.homeAddSec5Left img{width:100%;}
.homeAddSec5Left{position:absolute; top:0; left:0; height:100%;}
.homeAddSec5Right{position:relative; background:#ffffff; padding:40px 40px 60px 40px; margin:50px 0 120px 0; border:1px solid #eeeeee; box-shadow:5px 10px 10px 0 rgba(0,0,0,.3);}
.homeAddSec5Right h3{font-weight:700; font-size:18px; line-height:22px; color:var(--primary);}
.homeAddSec5Right p{font-size:14px;}
.homeAddSec5RightLogo{position:absolute; bottom:-50px; left:50%; transform:translateX(-50%); background:#ffffff; border:2px solid var(--primary); border-radius:50%; width:100px; height:100px; display:flex; align-items:center; justify-content:center;}
.homeAddSec5RightLogo img{width:70px !important;}
.homeAddSec5RightBottom{margin-bottom:50px;}
.homeAddSec5RightBottom div{display:flex; justify-content:space-between; position:relative; padding-bottom:5px; margin-bottom:20px;}
.homeAddSec5RightBottom div:after{content:""; position:absolute; bottom:0; left:0; width:100%; height:2px;}
.homeAddSec5RightBottom .top h3{font-weight:700; font-size:18px; line-height:28px; color:var(--primary); margin:0;}
.homeAddSec5RightBottom .top::after,
.homeAddSec5RightBottom .bottom::after {content:""; position:absolute; bottom:0; left:0; width:0%; height:2px; background:var(--primary); transition:width 2s ease-in-out;}
.homeAddSec5RightBottom .top.active::after,
.homeAddSec5RightBottom .bottom.active::after {width:100%;}
.homeAddSec5RightBottom .bottom h3{font-weight:700; font-size:18px; line-height:28px; margin:0;}
.homeAddSec5RightBottom .bottom:after{background:#000000;}




/*--------------------------------------------------------------
# Home Additional Section 5
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Home Additional Section 6
--------------------------------------------------------------*/
.homeAddSec6{position:relative;}
.homeAddSec6:before{content:''; position:absolute; top:0; left:0; background:rgb(110 169 220 / 95%); width:100%; height:100%; z-index:0;}
.homeAddSec6Left,.homeAddSec6Right{position:relative; z-index:1;}
.homeAddSec6Left h2{margin:0 0 30px 0; font-size:26px; line-height:26px; font-weight:800; color:#ffffff;}
.homeAddSec6Left p{width:350px; max-width:100%; color:#ffffff;}



/*--------------------------------------------------------------
# Home Additional Section 6
--------------------------------------------------------------*/


.hAddSec4{position:relative;}
.hAddSec4:before{content:''; position:absolute; top:0; left:0; width:100%; height:100%; opacity:.95; background:var(--primary);}
.hAddSec4Head{position:relative; z-index:1;}
.hAddSec4Head h2{margin:0; font-size:26px; line-height:26px; font-weight:800;}
.headerWraper{width:1510px; max-width:100%; margin-left:auto;}
.wrapper{display:flex; width:1510px; max-width:100%; margin-left:auto;}
.wrapper>div#quote-slider{width:25%; height:400px;}
.wrapper>div#image-slider{width:75%;}
#image-slider .splide__slide{position:relative; margin:0 0 0 8px;}
#image-slider .splide__slide:after{content:''; position:absolute; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,.8); transition:.3s;}
#image-slider .splide__slide img{width:100%;}
@media (max-width:1440px){
  .wrapper>div#quote-slider{padding-left:20px;}
  .headerWraper{padding-left:20px; padding-right: 20px;}
}
@media (max-width:1366px){
  .wrapper>div#quote-slider{padding-left:20px;}
}
#image-slider .splide__slide.is-active:after{transform:scale(0);}
#quote-slider .splide__slide.is-active .hAddSec4Col .count{background:#000000; color:#ffffff;}
.hAddSec4Col{display:flex; gap:20px;}
.hAddSec4Col .count{width:70px; height:70px; border:2px solid #000000; display:flex; justify-content:center; align-items:center; font-size:24px; font-family:"Cinzel",serif; font-weight:700;}
.hAddSec4Col .count span{position:relative; z-index:1;}
.hAddSec4Col:after{content:''; position:absolute; bottom:0; left:35px; width:2px; height:42px; background:#000000;}
.hAddSec4Col .info h3{font-size:15px; font-family:"Cinzel",serif; font-weight:700; margin:0;}



/*--------------------------------------------------------------
# Consumer Privacy Policy Additional Section 1
--------------------------------------------------------------*/

.cppAddSec1 .cppAddSec1Inner h3{font-size:16px; line-height:16px; font-weight:700; margin-bottom:10px;}
.cppAddSec1 .cppAddSec1Inner p{font-size:15px;}
.cppAddSec1 .cppAddSec1Inner ul{margin:10px 0 20px 0;}
.cppAddSec1 .cppAddSec1Inner ul li{display:block; padding-left:25px; background:url(assets/img/checkbox.png) 0 3px no-repeat; margin:5px 0; font-weight:500; font-size:15px;}
.cppAddSec1 .cppAddSec1Inner table{width:100%;}
.cppAddSec1 .cppAddSec1Inner table tr{border:1px solid #cccccc;}
.cppAddSec1 .cppAddSec1Inner table tr td{border:1px solid #cccccc; padding:10px 20px;}


/*--------------------------------------------------------------
# Consumer Privacy Policy Additional Section 1
--------------------------------------------------------------*/

.mainBanner{position:relative;}
.mainBanner:before{content:""; position:absolute; top:0; left:0; width:100%; height:100%; display:block; background:rgba(0,0,0,0.5);}
.mainBannerText{padding:150px 0 50px 0; position:relative; z-index:1;}
.mainBannerText h2{text-align: center;color:#ffffff; font-size:38px; line-height:38px; text-transform:uppercase;}
.mainBannerText p{text-align:center; color: #ffffff}
.breadcrumb{margin:0; display:flex; align-items:center; justify-content: center;}
.breadcrumb a{color:#ffffff; text-transform:uppercase; font-size:13px; line-height:16px;}
.breadcrumb span{color:#ffffff; text-transform:uppercase; font-size:13px; line-height:16px;}
.breadcrumb span i{font-size:13px; line-height:15px;}
.brd-bottom{border-bottom: 2px solid var(--brownColor);}





/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/

.cAddSec1 .cAddSec1LeftCol h4{font-weight:700; color:var(--primary);}
.cAddSec1 .cAddSec1LeftCol h2{font-size:50px; line-height:50px;}
.cAddSec1 .cAddSec1LeftCol p{font-size:18px; line-height:26px;margin: 0;}

.cAddSec1 .cAddSec1RightCol .form-fields, .cAddSec1 .cAddSec1RightCol .form-fields2, .cAddSec1 .cAddSec1RightCol .form-fields3{border-color: #000000;color: #000000;}
.cAddSec1 .cAddSec1RightCol input::placeholder {color: #000000;}
.cAddSec1 .cAddSec1RightCol textarea::placeholder {color: #000000;}

.cAddSec2{position:relative; z-index:1111; margin-bottom:-140px;}
.cAddSec2Container{padding:50px 40px; background-color:#f3f3f3; border:1px solid #bbbbbb;}
.contact-details__info{position:relative; display:block;}
.contact-details__info li{position:relative; display:flex; align-items:center;}
.contact-details__info li .icon{height:80px; width:80px; border-radius:50%; background-color:#ffffff; display:flex; align-items:center; justify-content:center; transition:all .5s ease;}
.contact-details__info .icon{background:#ffffff;}
.contact-details__info li:hover .icon{background:var(--primary);}
.contact-details__info li .icon img{width:50px;}
.contact-details__info li:hover .icon img{filter:brightness(0) invert(1);}
.contact-details__info li .text{margin-left:15px;}
.contact-details__info li .text h6{margin:0; font-size:22px; line-height: 1.1;}
.contact-details__info li .text a, .contact-details__info li .text span{font-size: 13px;}
.contact-details__info li .text span br{display:none;}



/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/
.aboutAddSec1 .aboutAddSec1Right img{width:100%;}
.aboutAddSec1Right, .aboutAddSec1Right div {height: 100%;}
.aboutAddSec1Right div img {object-fit: cover; object-position: center; height: 100%; width: 100%;}
.aboutAddSec1Left h2{font-weight:900; font-size:30px; line-height:40px; color:var(--primary);}
.aboutAddSec1Left h3{
	font-size: 18px;
    font-family: "Cinzel", serif;
    font-weight: 800;
}
.aboutAddSec1Left p{font-size:18px; line-height:22px;}
.aboutAddSec1LeftInner{background:#ffffff; margin-left:-150px; padding:40px; margin-top:30px; border:1px solid #eeeeee; box-shadow:5px 10px 10px 0 rgba(0,0,0,.3); position:relative; z-index:1;}
.aboutAddSec1LeftInner h3{font-weight:700; font-size:20px; line-height:30px; color:var(--primary);}
.aboutAddSec1LeftInner p{font-size:18px; line-height:22px; text-align:justify;}
.aboutAddSec1Bottom{display:flex; flex-wrap:wrap; gap:50px; margin-top:50px; justify-content:space-between;}
.aboutAddSec1BottomStats{text-align:center;}
.aboutAddSec1BottomStats h2{margin:0; color:var(--primary); font-weight:800; font-size:40px; line-height:40px; text-transform:uppercase;}
.aboutAddSec1BottomStats p{font-size:18px; font-family:"Cinzel",serif; font-weight:800;}



.aboutAddSec1Left button.accordion-button{font-size:18px; padding:12px 0; background:transparent;}
.aboutAddSec1Left .accordion-button::after{position:absolute; content:"\f068"; font-family:"Font Awesome 6 Free"; font-weight:900; line-height:25px; border-radius:50%; right:16px; font-size:18px; line-height:18px; color:var(--primary); text-align:center; z-index:1; background-image:none; transform:none;}
.aboutAddSec1Left .collapsed::after{content:"\2b" !important;}
.aboutAddSec1Left .accordion-body{padding-inline:0; padding-bottom:14px; padding-top:0;}
.aboutAddSec1Left .accordion-body ul li{list-style:disc; list-style-position:inside;}
.aboutAddSec1Left  .accordion-body ul li::marker{color:var(--primary);}
.aboutAddSec1Left .accordion-button:not(.collapsed),.accordion-button .collapsed{color:var(--primary); background-color:transparent; box-shadow:none;}
.aboutAddSec1Left .accordion-item{color:#000000; background-color:transparent; border-bottom:1px solid var(--primary);}
.aboutAddSec1Left .accordion-item{background-color:transparent; border-bottom:1px solid #4d4d4d;}
.aboutAddSec1Left .accordion-item:nth-last-of-type(1){border-bottom:none !important;}
.aboutAddSec1Left .accordion-button:focus{z-index:3; border-color:transparent; outline:0; box-shadow:none;}





.team-block,.team-block .inner-box{position:relative;}
.team-block .image-box{position:relative;}
.team-block .image-box .image{position:relative; overflow:hidden; margin-bottom:0; z-index:1; border:2px solid #000000; padding:5px;}
.team-block .image-box .image img{width:100%; -webkit-transition:all .4s ease; transition:all .4s ease; filter:grayscale(1);}
.team-block .info-box{position:relative; padding:19px 70px 19px 30px; background-color:#000000; max-width:322px; margin:-35px auto 0; -webkit-box-shadow:0 10px 60px rgba(0,0,0,.07); box-shadow:0 10px 60px #00000012; z-index:2; min-height:112px;}
.team-block .info-box:before{content:""; position:absolute; left:0; top:0; height:100%; width:100%; width:0; background-color:var(--primary); -webkit-transition:all .3s ease; transition:all .3s ease;}
.team-block .info-box:after{content:""; position:absolute; left:0; height:100%; width:100%; top:50%; width:9px; height:45px; background-color:var(--primary); -webkit-transform:translateY(-50%); transform:translateY(-50%); -webkit-transition:all .3s ease; transition:all .3sease;}
.team-block .info-box h4{position:relative; font-size:18px; font-family:"Cinzel",serif; font-weight:800; color:#ffffff;}
.team-block .info-box .designation{position:relative; color:#999999; font-size:16px; line-height:18px; font-weight:700; display:block; -webkit-transition:all .3sease; transition:all .3sease;}
.team-block .share-icon{position:absolute; top:50%; right:20px; width:45px; height:45px; line-height:43px; font-size:19px; color:#ffffff; border:1px solid #E7E7E7; -webkit-transition:all .3s ease; transition:all .3s ease; -webkit-transform:translateY(-50%); transform:translateY(-50%); text-align:center; z-index:3;}
.team-block .social-links{position:absolute; right:20px; bottom:100%; margin-bottom:-10px; display:-webkit-box; display:-ms-flexbox; display:flex; -webkit-box-align:center; -ms-flex-align:center; align-items:center; -webkit-box-orient:vertical; -webkit-box-direction:normal; -ms-flex-direction:column; flex-direction:column; -webkit-transform:scaleY(0); transform:scaleY(0); -webkit-transform-origin:bottom; transform-origin:bottom; z-index:3; opacity:0; visibility:hidden; -webkit-transition:all .4s ease; transition:all .4s ease;}
.team-block .social-links a{position:relative; height:46px; width:46px; line-height:46px; text-align:center; background-color:#000000; border:1px solid #ffffff; margin-top:10px; -webkit-transition:all .3s ease; transition:all .3s ease;}
.team-block .social-links a * {color: #fff;}
.team-block .inner-box:hover .image img{-webkit-transform:scale(1.1); transform:scale(1.1); filter:grayscale(0);}
.team-block .inner-box:hover .info-box:before{width:100%;}
.team-block .inner-box:hover .info-box:after{background-color:var(--primary);}
.team-block .inner-box:hover .info-box .name,.team-block .inner-box:hover .info-box .designation{color:#ffffff;}
.team-block .inner-box:hover .info-box .share-icon{background-color:var(--primary);}
.team-block .inner-box:hover .social-links{-webkit-transform:scaleY(1); transform:scaleY(1); opacity:1; visibility:visible;}
.team-block .social-links a:hover{color:#ffffff; background-color:var(--primary);}




/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Services
--------------------------------------------------------------*/
.cusHeader h1 br{display: none;}
.serviceAddSec1Col{
	padding-top: 15px;
    padding-bottom: 15px;
	background: url(assets/img/service-hover.png) center bottom no-repeat;
    background-size: 0 3px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.serviceAddSec1ColIcon{
	display: inline-block;
    background: none;
    border: 2px solid #eeeeee;
    padding: 19px;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    position: relative;
	margin-bottom: 20px;
}
.serviceAddSec1ColIcon img {
    width: 45px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.serviceAddSec1Col h3{
	font-size: 22px;
    line-height: 1.2;
	position: relative;
    display: inline-block; text-align: center;
    padding-bottom: 14px;
}
.serviceAddSec1Col h3 br{display: none;}
.serviceAddSec1Col h3:before {
    content: '';
    position: absolute;
    left: 51%;
    bottom: 0;
    width: 15%;
    height: 3px;
    background: var(--primary);
}
.serviceAddSec1Col h3:after {
    content: '';
    position: absolute;
    right: 51%;
    bottom: 0;
    width: 15%;
    height: 3px;
    background: #000000;
}
.serviceAddSec1ColBtn{
    display: inline-block;
    padding: 6px 20px;
    background: var(--primary);
    color: #ffffff;
    position: absolute;
    top: -38px;
    left: 50%;
    transform: translate(-50%);
}
.serviceAddSec1ColBtn:hover{color: #ffffff;}
.serviceAddSec1Col:hover {
    background: url(assets/img/service-hover.png) center bottom no-repeat #f5f5f5;
    background-size: 100% 3px;
}
.serviceAddSec1Col:hover .serviceAddSec1ColBtn {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


/*--------------------------------------------------------------
# Services
--------------------------------------------------------------*/



/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
.footerAddSec1{color:#ffffff; background:#000000;}
.footerAddSec1Col img{width:200px;}
.footerAddSec1Col ul li a{color:#ffffff; display:block; font-size:14px; line-height:22px; text-transform:uppercase;}
.footerAddSec1Col ul li a br{display:none;}
.footerAddSec1Col ul li a:hover{color:var(--primary);}
.footerAddSec1Col h4{margin:0 0 20px 0; font-size:20px; line-height:22px; font-weight:800; color:var(--primary);}
.footerAddSec1Col a{color:#ffffff; display:block; font-size:14px; line-height:22px; text-transform:uppercase;}
.footerAddSec1Col p{font-size:15px; line-height:22px; margin-top:20px; font-weight:600;}
.footerAddSec2{background:#82e2df; text-align:center; padding:5px 0;}
.footerAddSec2 p{color:#ffffff; margin:0; font-weight:600;}
.footerAddSec2 p a{color:#ffffff; margin:0; font-weight:500; display:inline-block;}
.pt-6{padding-top:5rem !important;}
.pb-6{padding-bottom:5rem !important;}
.py-6{padding-top:5rem !important; padding-bottom:5rem !important;}
.py-7{padding-top:7rem !important; padding-bottom:7rem !important;}
.form-fields,.form-fields2,.form-fields3{width:100% !important; box-sizing:border-box; padding:5px 0; font-size:15px; margin-bottom:15px; background:none; border:none; color:#ffffff; border-bottom:2px solid #ffffff; -webkit-transition:0.3s ease-in-out !important; -moz-transition:0.3s ease-in-out !important; -ms-transition:0.3s ease-in-out !important; -o-transition:0.3s ease-in-out !important; transition:0.3s ease-in-out !important;}
.form-fields:focus,.form-fields2:focus,.form-fields3:focus{border:none; border-bottom:2px solid #ffffff; outline:none !important;}
.form-fields3{height:130px;}
.wpcf7-submit,.ln-widgetBox.search .searchButton a{float:right; position:relative; padding:10px 30px; font-weight:700; display:inline-block; text-transform:uppercase; border:1px solid #ffffff; background:var(--primary); color:#ffffff; margin-top:15px;}
.wpcf7-submit:hover{border:1px solid #000000; background:#000000; color:#ffffff;}
div.wpcf7 img.ajax-loader{float:left;}
.wpcf7-list-item{display:inline-block; margin-right:10px;}
div.wpcf7-response-output{float:left;}
.wpcf7-not-valid-tip{display:none !important;}
.wpcf7-not-valid{border-bottom:2px solid red !important; border:none;}
::placeholder{font-size:14px; text-transform:uppercase; color:#ffffff;}
.wpcf7 form.invalid .wpcf7-response-output,.wpcf7 form.unaccepted .wpcf7-response-output,.wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#ffffff;}
.modal-header h1{text-transform:uppercase; font-weight:700;}
.modal-body .form-fields,.modal-body .form-fields2,.modal-body .form-fields3{border-bottom:1px solid #cccccc; color:#000000; font-weight:500;}
.modal-body::placeholder{color:#000000 !important; font-weight:500; font-size:12px;}
.modal-body .wpcf7 form.invalid .wpcf7-response-output,.modal-body .wpcf7 form.unaccepted .wpcf7-response-output,.modal-body .wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#000000;}
@media (min-width:1400px){
  .container,.container-lg,.container-md,.container-sm,.container-xl,.container-xxl{max-width:1140px;}
}




/* Connect Page */
.connectMain{background: var(--darkBlue);}
.connectMainTop ul li{display: flex;gap: 15px; justify-content: center; align-items: center;}
.connectMainTop ul{display:flex;justify-content:space-between;gap:70px;}
.connectMainTop ul li i{display: inline-block;width: 40px;height: 40px;font-size: 15px;line-height: 40px;background: var(--darkBlue);color: #ffffff;text-align: center;border-radius: 50%;transition:0.3s;}
.connectMainTop ul li a ,.connectMainTop ul li p  {font-size: 15px;line-height: 20px;font-weight: 600;color:var(--darkBlue);margin: 0 !important;}	
.connectMainLeft h2 , .connectMainRight h2{margin: 0 0 10px 0;font-size: 24px;line-height: 34px; color: #ffffff; font-weight: 600;text-transform: uppercase;}
.connectMainLeft p{
    font-size: 18px;
    line-height: 25px;
    margin-bottom: 20px;
    color: #ffffff;
}	
.connectMainRight p{color: #ffffff;}
.connectMain .form-fields, .connectMain .form-fields2, .connectMain .form-fields3{border-bottom:2px solid var(--lightBlue);color:var(--lightBlue);}
.connectMain ::placeholder{color:#ffffff !important;}	
.connectMain .wpcf7-submit{color: #ffffff !important;background: var(--lightBlue) !important; font-size: 13px;line-height: 14px;}
.connectMainRight a{background: var(--lightBlue);color: #fff;border-radius: 50px;padding: 8px 25px;font-size: 13px;line-height: 14px;display: inline-block;text-transform: uppercase;font-weight:600;}
.connectMainTop ul li:hover i{transform:scale(1.1);}
.connectMainRight a:hover , .connectMain .wpcf7-submit:hover {background: var(--lightBlue) !important;}


.px-cus-1{padding: 0 1px;}

.tportfolio {position: relative;overflow: hidden; border-right: 4px solid white; border-bottom: 4px solid white;}
.tportfolio__img {position: relative;}
.tportfolio__img img {max-width: 100%; -webkit-filter: grayscale(100%); -ms-filter: grayscale(100%); -moz-filter: grayscale(100%); filter: grayscale(100%);}
.tportfolio__img h3{
    font-size: 10px;
    color: #ffffff;
    text-transform: uppercase;
    text-align: center;
    line-height: 20px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    width: 140px;
    display: block;
    background: #79A70A;
    background: linear-gradient(#F70505 0%,#8F0808 100%);
    box-shadow: 0 3px 10px -5px rgb(0 0 0);
    position: absolute;
    top: -10px;
    right: -50px;
}
.tportfolio__text {
    position: absolute;
    bottom: -50px;
    left: 0;
    padding: 25px 20px;
    z-index: 22;
    right: 0;
    background: #fff;
    margin: 15px;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.3s ease-out 0s;
    -moz-transition: all 0.3s ease-out 0s;
    -ms-transition: all 0.3s ease-out 0s;
    -o-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
}
.tportfolio__text h3 {
	margin: 0;
    color: #000000;
    display: inline-block;
    width: 80%;
    font-size: 14px;
    line-height: 18px;
    font-family: "Cinzel", serif;
    font-weight: 800;
    text-transform: uppercase;
}
.tportfolio__text h4 {
	font-weight: 800;
    color: var(--primary);
    margin: 0;
    font-size: 14px;
    line-height: 18px;
}
.portfolio-plus {position: absolute;right: 15px;top: 50%;transform: translateY(-50%);}
.portfolio-plus a {
    height: 45px;
    width: 45px;
    line-height: 43px;
    text-align: center;
    border-radius: 50%;
    border: 2px solid #eee;
    font-size: 15px;
    color: #111;
    display: inline-block;
}
.tportfolio:hover .tportfolio__img img {-webkit-filter: grayscale(0);-ms-filter: grayscale(0);-moz-filter: grayscale(0);filter: grayscale(0);}
.tportfolio:hover .tportfolio__text {bottom: 0;opacity: 1;visibility: visible;}
.portfolio-plus a:hover {background: var(--primary);border-color: var(--primary);color: #ffffff;}



ul#image-gallery li img{width: 100%;}


.single-properties #header {
    background: #000000;
}
.bottomHeader {
    margin-top: 140px;
    margin-bottom: 20px;
}

.pSingleLeftSec h1{
	margin: 0;
    font-size: 26px;
    line-height: 26px;
    font-weight: 800;
    color: var(--primary);
}
.pSingleLeftSec h4{
    font-weight: 800;
    color: #777777;
    font-size: 13px;
}
.pSingleLeftSec ul{margin-top: 20px; border-top: 1px solid #dddddd; padding-top: 10px;}
.pSingleLeftSec ul li {
    position: relative;
    padding-left: 20px;
    font-size: 15px;
    line-height: 25px;
}
.pSingleLeftSec ul > li > ul{margin-top: 0; border: 0;padding-top: 0}
.pSingleLeftSec ul li:before{
    content: '';
    position: absolute;
    top: 8px;
    left: 0;
    width: 10px;
    height: 10px;
    background: var(--primary);
}


.pSingleRightSec{margin-top: 30px;}
.pSingleRightInnerSec{
    border: 1px solid #dddddd;
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.pSingleRightInnerSec div{line-height: 1px}
.pSingleRightInnerSec div i{
    font-size: 35px;
    line-height: 40px;
    color: var(--primary);
    padding-right: 15px;
}
.pSingleRightInnerSec div.flaticonDiv i{font-size: 30px;line-height: 30px;}
.pSingleRightInnerSec h4{margin: 0;}
.pSingleRightInnerSec a{font-size: 12px;line-height: 12px;text-decoration: underline;}
.pSingleRightInnerSec div p{margin-top: 10px !important;}


.highlights{}
.highlights h3{
	font-size: 18px;
    font-family: "Cinzel", serif;
    font-weight: 800;
}
.highlights ul{
	margin-top: 20px;
    border-top: 1px solid #dddddd;
    padding-top: 10px;
}
.highlights ul li{
	padding-left: 25px;
    background: url(assets/img/check-mark.png) 0 3px no-repeat;
    margin: 5px 0;
    display: flex;
    justify-content: space-between;
}
.highlights ul li span{}




@media (max-width:991px) {
	.homeAddSec4 .homeAddSec4Inner h3 {padding: 0 20px;}
	.homeAddSec4 .homeAddSec4Inner p {padding: 0 20px;}
	.homeAddSec4 {padding: 2rem 0 2rem 0;}
	.homeAddSec4::before {top: -84px; height: 100px;}
	.homeAddSec4 .homeAddSec4Inner ul {flex-wrap: wrap;}
	.homeAddSec4 .homeAddSec4Inner ul li {width: 100%;}
	.homeAddSec4 .homeAddSec4Inner ul li:nth-child(2) {margin: 0;}
	.homeAddSec4 .homeAddSec4Inner ul li {font-size: 14px;line-height: 18px;}


}
@media (max-width:768px) {
	.homeAddSec4 .homeAddSec4Inner h3 {font-size: 25px;}
	.homeAddSec4 {padding: 7rem 0 2rem 0;}
	.homeAddSec4::before {top: 0;}

}
@media (max-width:481px) {
	.contactNo{display: none;}
	.homeAddSec4 .homeAddSec4Inner h3 {font-size: 20px; line-height: 28px;}

}
@media (max-width:320px) {

}

@media (min-width:1280px) and (max-width:1366px){
	.navbar .dropdown .dropdown ul{left:-90%;}
	.navbar .dropdown .dropdown:hover>ul{left:-100%;}
  }

.disclaimer p{font-size:14px;}
.disclaimer p a{
	color: #4f8a6d;
    text-decoration: underline;
    font-weight: 500;
}

/***************** Events Page *******************/

.eventsBox {
	position: relative;
    overflow: hidden; 
}
.eventsImg {
	position: relative;
	overflow: hidden; 
}
.eventsImg img {
    max-width: 100%; 
	transform: scale(1);
	 -webkit-transition: 0.3s ease-in-out !important;
    -moz-transition: 0.3s ease-in-out !important;
    -ms-transition: 0.3s ease-in-out !important;
    -o-transition: 0.3s ease-in-out !important;
}
.eventsBox:hover .eventsImg img { 
	transform: scale(1.05);
}
.eventsText {
	padding: 25px 20px; 
    background: #eeeeee;  
}
.eventsText h3 {
    margin: 0; 
    display: inline-block;
    width: 80%;
    font-size: 18px;
    line-height: 24px;
    font-family: "Cinzel", serif;
    font-weight: 800;
    text-transform: uppercase;
}
.eventsText h5 { 
    font-size: 17px;
    color: var(--primary);
    font-weight: 600;
}
.eventsIcon {
	position: absolute;
    right: 15px;
    bottom: 45px;
}
.eventsIcon a {
    height: 45px;
    width: 45px;
    line-height: 43px;
    text-align: center;
    border-radius: 50%;
    border: 2px solid #cfcfcf;
    font-size: 15px;
    color: #111;
    display: inline-block;
}
.eventsIcon a:hover {
    background: var(--primary);
    border-color: var(--primary);
    color: #ffffff;
}
 
.single-events #header {
    background: #000000;
}
.singleEventsImg img {
    max-width: 100%;
}
.singleEventsText a {
    border: 1px solid var(--primary);
    background: var(--primary);
    color: #ffffff;
    position: relative;
    padding: 10px 30px;
    font-weight: 700;
    display: inline-block;
    text-transform: uppercase;
}
.singleEventsText a:hover {
    border: 1px solid var(--primary);
    background: transparent;
    color: var(--primary);
}
.singleEventsText h4 {
    margin-bottom: 20px; 
    font-size: 20px;
    color: var(--primary);
    font-weight: 600;
}






@media (max-width:991px){
	.mainBanner p br{display:none;}
	
	.mainBannerText h2{
    	font-size: 28px;
    	line-height: 28px;
	}
}
@media (max-width:576px){
	.mainBanner p {line-height: 22px; margin:0;}
	.mainBannerText h2{font-size: 28px; line-height: 28px;}
}
