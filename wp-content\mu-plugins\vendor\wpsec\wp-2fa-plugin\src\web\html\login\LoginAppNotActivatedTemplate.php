<?php

namespace Wpsec\twofa\web\html\login;


use Wpsec\twofa\Services\TOPT;

/**
 * Login App Not Activated Template.
 *
 * @package Wpsec
 * @subpackage Wpsec/web/html/login
 */
class LoginAppNotActivatedTemplate {


	/**
	 * Render login app template.
	 *
	 * @since 1.0.0
	 */
	public static function render( $user_id, $qr_callback ) {       ?>
		<div id="wpsec_2fa_app_first_dialog_content" class="ui-dialog-content ui-widget-content">
			<div id="wpsec_2fa_login_vertical_line"></div>

			<p class="wpsec-2fa-text-align-center">
				<?php
				/* translators: %s: search term */
				printf( __( 'Select %1$sNext%2$s to set up authentication app.', 'wpsec-wp-2fa' ), '<strong>', '</strong>' );
				?>
			</p>

			<div id="wpsec_2fa_login_mail_first_check_conf">
				<div class="wpsec-2fa-display-flex one_button_only">
					<div class="wpsec_2fa_admin_progress_bar">
						<span class="wpsec_2fa_dot wpsec_2fa_dot_active wpsec_2fa_login_dot"></span>
						<span class="wpsec_2fa_dot"></span>
						<span class="wpsec_2fa_dot"></span>
					</div>
					<button type="button"
							class="file-editor-warning-dismiss button button-primary wpsec_2fa_login_button_next">
						<?php echo __( 'Next', 'wpsec-wp-2fa' ); ?>
					</button>
				</div>
			</div>
		</div>
		<div id="wpsec_2fa_app_scan_dialog_content" class="wpsec-2fa-hidden ui-dialog-content ui-widget-content">
			<p class="wpsec_2fa_paragraf_style">
				<?php echo __( 'Install your authenticator app, such as Google Authenticator, on your device, then use it to scan the QR code below.', 'wpsec-wp-2fa' ); ?>
			</p>
			<br>
			<div class="wpsec-2fa-qr-container">
				<?php
				$qr_code = $qr_callback( $user_id );
				if ( $qr_code ) {
					?>
					<img src="<?php echo $qr_code; ?>">
					<?php
				} else {
					echo __( 'QR code can not be loaded!', 'wpsec-wp-2fa' );
				}
				?>

				<div class="wpsec_2fa_help_notification_section">
					<?php if ( $qr_code ) : ?>
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM13.27 16.77C13.27 17.45 12.72 18.02 12.02 18.02C11.32 18.02 10.8 17.46 10.8 16.77C10.8 16.08 11.34 15.55 12.02 15.55C12.7 15.55 13.27 16.07 13.27 16.77ZM15.69 9.43C15.69 12.47 12.87 12.14 12.87 13.31V14.5H11.15V13.3C11.15 10.56 13.92 11.37 13.92 9.41C13.92 8.31 13.14 7.81 11.99 7.81C10.65 7.81 10.09 8.52 10.09 9.8H8.31C8.31 7.47 9.75 6.12 11.93 6.12C13.94 6.11 15.69 7.25 15.69 9.43Z" fill="#111111"/>
					</svg>
					<?php endif; ?>
					<div class="wpsec_2fa_help_notification_text">
						<p>
							<?php echo __( 'Install an authenticator app, such as Google Authenticator, on your device.', 'wpsec-wp-2fa' ); ?>

						</p>
						<p>
							<?php echo __( 'Open the authenticator app.', 'wpsec-wp-2fa' ); ?>

						</p>
						<p>
							<?php
							/* translators: %s: search term */
							printf( __( 'Select %1$s"Add"%2$s or Begin to %1$sSet up%2$s.', 'wpsec-wp-2fa' ), '<strong>', '</strong>' );
							?>

						</p>
						<p>
							<?php echo __( 'Select the option to scan a QR code, then scan the code to complete the set up.', 'wpsec-wp-2fa' ); ?>

						</p>
					</div>
				</div>


			</div>
			<div class="wpsec-2fa-display-flex">
				<button type="button" id='wpsec_2fa_app_scan_close_button' class="file-editor-warning-dismiss button button-secondary wpsec_2fa_login_button_back">
					<?php echo __( 'Back', 'wpsec-wp-2fa' ); ?>
				</button>
				<p class="wpsec_2fa_admin_progress_bar">
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot"></span>
				</p>
				<button type="button" id="wpsec_2fa_login_button"
						class="file-editor-warning-dismiss button button-primary wpsec_2fa_login_button_next">
					<?php echo __( 'Next', 'wpsec-wp-2fa' ); ?>
				</button>
			</div>
		</div>

		<div id="wpsec_2fa_app_check_dialog_content" class="wpsec-2fa-hidden ui-dialog-content ui-widget-content">
			<p class="wpsec_2fa_paragraf_style">
				<?php
				/* translators: %s: search term */
				printf( __( 'Enter the 6-digit code generated by the app, then select %1$sVerify%2$s.', 'wpsec-wp-2fa' ), '<strong>', '</strong>' );
				?>
			</p>

			<br>
			<input type="text"
				id="wpsec_2fa_login_app_code_check"
				name="wpsec_2fa_login_app_code_check"
				value="" placeholder="<?php echo __( 'Authentication App Code', 'wpsec-wp-2fa' ); ?>">
			<div class="wpsec-2fa-display-flex">
				<button type="button" id='wpsec_2fa_app_back_button' class="file-editor-warning-dismiss button button-secondary wpsec_2fa_login_button_back">
					<?php echo __( 'Back', 'wpsec-wp-2fa' ); ?>
				</button>
				<p class="wpsec_2fa_admin_progress_bar">
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
					<span class="wpsec_2fa_dot wpsec_2fa_dot_active"></span>
				</p>
				<input type="button" id="wp_wpsec_2fa_login_app_submit" class="button button-primary button-large"
					value="<?php echo __( 'Verify', 'wpsec-wp-2fa' ); ?>">
			</div>
		</div>
		<input type="hidden" id="wpsec_2fa_current_slide" value="0" />
		<?php
	}
}

