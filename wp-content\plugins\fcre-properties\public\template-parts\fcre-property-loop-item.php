<?php
$FCRE = Fcre_Global::getInstance();
$image = wp_get_attachment_image_src(get_post_thumbnail_id(get_the_ID()), 'full');
$image = $image[0];
$id = get_the_ID();
$title = get_the_title();
$content = get_the_content();
$property_type = fcre_get_option_label_from_meta(get_the_ID(), 'property_types', $FCRE->plugin_name . '-property-types');
$transaction_type = fcre_get_option_label_from_meta(get_the_ID(), 'transaction_types', $FCRE->plugin_name . '-transaction-types');
$property_status = fcre_get_option_label_from_meta(get_the_ID(), 'property_status', $FCRE->plugin_name . '-property-status');
// $property_status = get_post_meta($post->ID, 'status', true);

$address = get_post_meta($post->ID, 'address', true);
$site = get_post_meta($post->ID, 'site', true);
$building = get_post_meta($post->ID, 'building', true);
$building_size = get_post_meta($post->ID, 'building_size', true);
$building_class = get_post_meta($post->ID, 'building_class', true);
$lease_rate = get_post_meta($post->ID, 'lease_rate', true);
$year_built = get_post_meta($post->ID, 'year_built', true);
// $property_status = get_post_meta($post->ID, 'status', true);
$latitude = get_post_meta($post->ID, 'latitude', true);
$longitude = get_post_meta($post->ID, 'longitude', true);
?>
<a class="fcre-listing-link" href="<?php the_permalink(); ?>">
    <div class="fcre-listing-box">
        <div class="fcre-listing-image">
            <img src="<?php echo $image; ?>" alt="<?php echo $title; ?>" />
        </div>
        <div class="fcre-listing-content">
            <?php if($property_status){ ?>
            <h4><?php echo $property_status; ?></h4>
            <?php } ?>
            <h2 class="fcre-listing-title">
               <?php echo $title; ?>
            </h2>
            
            <?php
            $address_words = explode(' ', trim($address));
            $word_count = count($address_words);
            
            if ($word_count > 3) {
                $start_part = implode(' ', array_slice($address_words, 0, $word_count - 3));
                $last_three = implode(' ', array_slice($address_words, -3));
            } else {
                $start_part = '';
                $last_three = $address;
            }
            ?>
            <p class="fcre-listing-address">
                <?php echo $start_part; ?>
                <br>
                <span class="fcre-address-highlight"><?php echo $last_three; ?></span>
            </p>

            
            
            <!--<p class="fcre-listing-address"><?php echo $address; ?></p>-->
        </div>
    </div>
</a>