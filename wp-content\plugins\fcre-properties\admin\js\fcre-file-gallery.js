jQuery(function($) {

    class FCREFileGallery {
        constructor(options) {
            this.container = $(options.container);
            this.list = this.container.find('.fcre-files-gallery-metabox-list');
            this.metaKey = options.metaKey;
            this.fileFrame = null;
    
            this.bindEvents();
            this.makeSortable();
        }
    
        bindEvents() {
            const self = this;
    
            self.container.on('click', '.file-gallery-add', function (e) {
                e.preventDefault();
                self.openMediaUploader(true, $(this));
            });
    
            self.container.on('click', '.change-file', function (e) {
                e.preventDefault();
                self.openMediaUploader(false, $(this));
            });
    
            self.container.on('click', '.remove-file', function (e) {
                e.preventDefault();
                $(this).closest('li').fadeOut(200, function () {
                    $(this).remove();
                    self.resetIndexes();
                });
            });
        }
    
        openMediaUploader(multiple, button) {
            const self = this;
            if (self.fileFrame) {
                self.fileFrame.close();
            }
    
            self.fileFrame = wp.media({
                title: button.data('uploader-title'),
                button: {
                    text: button.data('uploader-button-text'),
                },
                multiple: multiple
            });
    
            self.fileFrame.on('select', function () {
                const selection = self.fileFrame.state().get('selection');
                const listIndex = self.list.find('li').length;
    
                if (multiple) {
                    selection.map(function (attachment, i) {
                        self.addFile(attachment.toJSON(), listIndex + i);
                    });
                } else {
                    const attachment = selection.first().toJSON();
                    self.updateFile(button, attachment);
                }
            });
    
            self.fileFrame.open();
        }
    
        addFile(file, index) {
            const html = `
                <li>
                    <input type="hidden" name="${this.metaKey}[${index}][file]" value="${file.id}">
                    <a href="${file.url}" class="file-link ${this.getFileExtension(file.filename)}" target="_blank">${file.filename}</a>
                    <a href="#" class="change-file file-edit" data-uploader-title="Change File" data-uploader-button-text="Change File"></a>
                    <a href="#" class="remove-file file-remove"></a>
                </li>`;
    
            this.list.append(html);
        }
    
        updateFile(button, file) {
            const parent = button.closest('li');
            parent.find('input:hidden').val(file.id);
            parent.find('a.file-link').attr('href', file.url).attr('class', `file-link ${this.getFileExtension(file.filename)}`).text(file.filename);
        }
    
        resetIndexes() {
            this.list.find('li').each((i, li) => {
                $(li).find('input:hidden').attr('name', `${this.metaKey}[${i}][file]`);
            });
        }
    
        makeSortable() {
            const self = this;
            self.list.sortable({
                opacity: 0.6,
                stop: function () {
                    self.resetIndexes();
                }
            });
        }
    
        getFileExtension(filename) {
            return filename.split('.').pop();
        }
    }
    
    window.FCREFileGallery = FCREFileGallery;
    
    // --- Ab yaha tum apni initialization bhi kar sakte ho ---
    
    $(document).ready(function(){
    
        // Suppose agar tum multiple galleries banana chahte ho:
        $('.fcre-file-gallery').each(function(){
            new FCREFileGallery({
                container: '#' + $(this).attr('id'),
                metaKey: $(this).data('meta-key')
            });
        });
    
    });
    
    });
    