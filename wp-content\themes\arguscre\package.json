{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.6.0", "description": "Default WP Theme", "author": "The WordPress Contributors", "license": "GPL-2.0-or-later", "keywords": ["WordPress", "Theme"], "bugs": {"url": "https://core.trac.wordpress.org/"}, "homepage": "https://wordpress.org/themes/twentytwentyone/", "devDependencies": {"@wordpress/browserslist-config": "^4.1.2", "@wordpress/eslint-plugin": "^9.3.0", "@wordpress/stylelint-config": "^19.1.0", "autoprefixer": "^10.4.5", "chokidar-cli": "^3.0.0", "eslint": "^8.14.0", "minimist": "^1.2.6", "npm-run-all": "^4.1.5", "postcss": "^8.4.12", "postcss-calc": "^8.2.4", "postcss-cli": "^9.1.0", "postcss-css-variables": "^0.18.0", "postcss-custom-media": "^8.0.0", "postcss-discard-duplicates": "^5.1.0", "postcss-focus-within": "^5.0.4", "postcss-merge-rules": "^5.1.1", "postcss-nested": "^5.0.6", "rtlcss": "^3.5.0", "sass": "^1.51.0", "stylelint": "^13.13.1", "stylelint-config-recommended-scss": "^5.0.2"}, "rtlcssConfig": {"options": {"autoRename": false, "autoRenameStrict": false, "blacklist": {}, "clean": true, "greedy": false, "processUrls": false, "stringMap": []}, "plugins": [], "map": false}, "browserslist": ["extends @wordpress/browserslist-config"], "scripts": {"start": "chokidar \"**/*.scss\" -c \"npm run build\" --initial", "build:style": "sass assets/sass/style.scss:style.css --style=expanded --source-map", "build:style-editor": "sass assets/sass/style-editor.scss:assets/css/style-editor.css --style=expanded --source-map", "build:style-dark-mode": "sass assets/sass/style-dark-mode.scss:assets/css/style-dark-mode.css --style=expanded --source-map", "build:rtl": "rtlcss style.css style-rtl.css", "build:dark-rtl": "rtlcss assets/css/style-dark-mode.css assets/css/style-dark-mode-rtl.css", "build:print": "sass assets/sass/07-utilities/print.scss:assets/css/print.css --style=expanded --source-map", "build:ie": "postcss style.css -o assets/css/ie.css", "build:ie-editor": "postcss assets/css/style-editor.css -o assets/css/ie-editor.css", "build:stylelint": "stylelint **/*.css --fix --config .stylelintrc-css.json", "build": "run-s \"build:*\"", "watch": "chokidar \"**/*.scss\" -c \"npm run build\" --initial", "lint:scss": "stylelint **/*.scss", "lint-fix:scss": "stylelint **/*.scss --fix"}}